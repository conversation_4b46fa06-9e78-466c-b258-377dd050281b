<!-- ManageLineChannel.svelte -->
<script lang="ts">
	import { Button, Input, Label, Toggle } from 'flowbite-svelte';
	import {
		ClipboardSolid,
		EditOutline,
		CheckOutline,
		PlayOutline,
		FileCopyOutline
	} from 'flowbite-svelte-icons';
	import { t } from '$lib/stores/i18n';
	import { enhance } from '$app/forms';
	import { createEventDispatcher } from 'svelte';

	export let lineConnection = null;

	const dispatch = createEventDispatcher();

	let isEditMode = false;
	let isLoading = false;

	// Form data - will be populated when lineConnection changes
	let connectionName = '';
	let channelId = '';
	let channelSecret = '';
	let channelAccessToken = '';
	let lineProviderId = '';
	let lineProviderName = '';
	let isEnabled = true;
	let webhookUrl = '';

	// Form references
	let updateFormElement: HTMLFormElement;
	let statusFormElement: HTMLFormElement;
	let testFormElement: HTMLFormElement;

	// Watch for lineConnection changes to populate form
	$: if (lineConnection) {
		populateForm();
	}

	function populateForm() {
		if (lineConnection) {
			connectionName = lineConnection.name || '';
			channelId = lineConnection.channel_id || '';
			channelSecret = lineConnection.channel_secret || '';
			channelAccessToken = lineConnection.channel_access_token || '';
			lineProviderId = lineConnection.provider_id || '';
			lineProviderName = lineConnection.provider_name || '';
			webhookUrl = lineConnection.webhook_url || '';
			isEnabled = lineConnection.status !== 'disabled';
		}
	}

	function toggleEditMode() {
		isEditMode = !isEditMode;
		if (!isEditMode) {
			// If canceling edit, restore original values
			populateForm();
		}
	}

	async function handleSave() {
		if (!lineConnection || !updateFormElement) return;

		isLoading = true;
		updateFormElement.requestSubmit();
	}

	async function handleStatusToggle() {
		if (!lineConnection || !statusFormElement) return;

		isLoading = true;
		statusFormElement.requestSubmit();
	}

	async function handleTest() {
		if (!lineConnection || !testFormElement) return;

		isLoading = true;
		testFormElement.requestSubmit();
	}

	function handleClose() {
		isEditMode = false;
		// Reset form when closing
		if (lineConnection) {
			populateForm();
		}
		dispatch('close');
	}

	function copyWebhookUrl() {
		navigator.clipboard.writeText(webhookUrl);
		dispatch('showToast', { message: t('webhook_url_copied'), color: 'green' });
	}

	function showToastMessage(message: string, color: string = 'green') {
		dispatch('showToast', { message, color });
	}
</script>

<!-- Update Form -->
<form
	bind:this={updateFormElement}
	method="POST"
	action="?/update_line_connector"
	use:enhance={({ formData }) => {
		formData.append('connector_id', lineConnection.id);
		return async ({ result, update }) => {
			isLoading = false;
			if (result.type === 'success') {
				showToastMessage(t('connection_updated_successfully'), 'green');
				isEditMode = false;
				dispatch('connectionUpdated');
			} else if (result.type === 'failure') {
				showToastMessage(result.data?.error || t('update_failed'), 'red');
			}
			await update();
		};
	}}
	style="display: none;"
>
	<input type="hidden" name="name" bind:value={connectionName} />
	<input type="hidden" name="channel_id" bind:value={channelId} />
	<input type="hidden" name="channel_secret" bind:value={channelSecret} />
	<input type="hidden" name="channel_access_token" bind:value={channelAccessToken} />
	<input type="hidden" name="line_provider_id" bind:value={lineProviderId} />
</form>

<!-- Status Toggle Form -->
<form
	bind:this={statusFormElement}
	method="POST"
	action="?/toggle_line_status"
	use:enhance={({ formData }) => {
		formData.append('connector_id', lineConnection.id);
		formData.append('action', isEnabled ? 'enable' : 'disable');
		formData.append('reason', isEnabled ? 'Re-enabled by user' : 'Disabled by user');
		return async ({ result, update }) => {
			isLoading = false;
			if (result.type === 'success') {
				const action = isEnabled ? 'enabled' : 'disabled';
				showToastMessage(t(`channel_${action}_successfully`), 'green');
				dispatch('connectionUpdated');
			} else if (result.type === 'failure') {
				showToastMessage(result.data?.error || t('status_toggle_failed'), 'red');
				isEnabled = !isEnabled; // Revert toggle
			}
			await update();
		};
	}}
	style="display: none;"
></form>

<!-- Test Connection Form -->
<form
	bind:this={testFormElement}
	method="POST"
	action="?/test_line_connector"
	use:enhance={({ formData }) => {
		formData.append('connector_id', lineConnection.id);
		return async ({ result, update }) => {
			isLoading = false;
			if (result.type === 'success') {
				showToastMessage(t('connection_test_successful'), 'green');
			} else if (result.type === 'failure') {
				showToastMessage(result.data?.error || t('connection_test_failed'), 'red');
			}
			await update();
		};
	}}
	style="display: none;"
></form>

<div class="space-y-6">
	<!-- Status Toggle -->
	<!-- <div class="flex items-center justify-between rounded-lg bg-gray-50 p-4">
		<div>
			<h3 class="text-lg font-medium text-gray-900">{t('connection_status')}</h3>
			<p class="text-sm text-gray-600">
				{isEnabled ? t('connection_active') : t('connection_disabled')}
			</p>
		</div>
		<Toggle color="green" bind:checked={isEnabled} on:change={handleStatusToggle} disabled={isLoading} />
	</div> -->

	<!-- Connection Details -->
	<div class="space-y-4">
		<!-- Connection Name -->
		<div>
			<Label for="connection-name" class="mb-2 block text-sm font-medium text-gray-900">
				{t('connection_name')}
			</Label>
			<Input
				id="connection-name"
				bind:value={connectionName}
				placeholder={t('connection_name_placeholder')}
				disabled={!isEditMode}
				class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
			/>
		</div>

		<!-- Channel ID -->
		<div>
			<Label for="channel-id" class="mb-2 block text-sm font-medium text-gray-900">
				{t('channel_id')}
			</Label>
			<Input
				id="channel-id"
				bind:value={channelId}
				placeholder={t('channel_id_placeholder')}
				disabled={!isEditMode}
				class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
			/>
		</div>

		<!-- Channel Secret -->
		<div>
			<Label for="channel-secret" class="mb-2 block text-sm font-medium text-gray-900">
				{t('channel_secret')}
			</Label>
			<Input
				id="channel-secret"
				bind:value={channelSecret}
				placeholder={t('channel_secret_placeholder')}
				disabled={!isEditMode}
				class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
			/>
		</div>

		<!-- Channel Access Token -->
		<div>
			<Label for="channel-access-token" class="mb-2 block text-sm font-medium text-gray-900">
				{t('channel_access_token')}
			</Label>
			<Input
				id="channel-access-token"
				bind:value={channelAccessToken}
				placeholder={t('channel_access_token_placeholder')}
				disabled={!isEditMode}
				class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
			/>
		</div>

		<!-- LINE Provider ID -->
		<div>
			<Label for="line-provider-id" class="mb-2 block text-sm font-medium text-gray-900">
				{t('line_provider_id')}
			</Label>
			<Input
				id="line-provider-id"
				bind:value={lineProviderId}
				placeholder={t('line_provider_id_description')}
				disabled={!isEditMode}
				class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
			/>
		</div>

		<!-- LINE Provider Name -->
		<div>
			<Label for="line-provider-name" class="mb-2 block text-sm font-medium text-gray-900">
				{t('line_provider_name')}
			</Label>
			<Input
				id="line-provider-name"
				bind:value={lineProviderName}
				placeholder={t('line_provider_name_description')}
				disabled={!isEditMode}
				class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
			/>
		</div>

		<!-- Webhook URL -->
		<div>
			<Label class="mb-2 block text-sm font-medium text-gray-900">
				{t('webhook_url')}
			</Label>
			<div class="flex gap-2">
				<Input
					value={webhookUrl}
					disabled={true}
					class="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500"
				/>
				<Button
					type="button"
					color="alternative"
					size="sm"
					on:click={copyWebhookUrl}
					class="px-3 py-2"
				>
					<FileCopyOutline class="h-5 w-5" />
				</Button>
			</div>
		</div>
	</div>
</div>

<!-- Footer with action buttons -->
<div class="mt-6 flex w-full justify-between">
	<!-- <div class="flex gap-2">
		<Button type="button" color="blue" on:click={handleTest} disabled={isLoading}>
			<PlayOutline class="mr-2 h-5 w-5" />
			{isLoading ? t('testing') : t('test_connection')}
		</Button>
	</div> -->

	<div class="flex gap-2">
		{#if isEditMode}
			<Button type="button" color="green" on:click={handleSave} disabled={isLoading}>
				<CheckOutline class="mr-2 h-5 w-5" />{isLoading ? t('saving') : t('save_changes')}
			</Button>
			<Button type="button" color="light" on:click={toggleEditMode}>
				{t('cancel')}
			</Button>
		{:else}
			<!-- <Button type="button" color="light" on:click={handleClose}>
				{t('close')}
			</Button> -->
			<Button outline type="button" color="green" on:click={toggleEditMode}>
				<EditOutline class="mr-2 h-5 w-5" />
				{t('edit')}
			</Button>
		{/if}
	</div>
</div>