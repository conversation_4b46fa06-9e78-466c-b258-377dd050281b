<!-- ConversationView.svelte -->
<script lang="ts">
	import { onMount, onDestroy, createEventDispatcher } from 'svelte';
	import ConversationHeader from './ConversationHeader.svelte';
	import MessageList from './MessageList.svelte';
	import MessageInput from './MessageInput.svelte';
	import { conversationStore } from '$lib/stores/conversationStore';
	import { conversationService } from '$lib/services/conversationService';
	import { platformWebSocket } from '$lib/websocket/platformWebSocket';
	import type { Message } from '$lib/types/customer';
	import { getBackendUrl } from '$src/lib/config';
	import { services } from '$src/lib/api/features';
	import { PollingService } from '$lib/services/pollingService';
	import type { PollingConfig } from '$lib/services/pollingService';

	import { page } from '$app/stores';

	export let customerId: number;
	export let platformId: number;
	export let ticketId: number | null = null;
	export let users: any[] = [];
	export let priorities: any[] = [];
	export let statuses: any[] = [];
	export let topics: any[] = [];
	export let access_token: string = '';
	// Removed latest_ticket_owner_id and currentLoginUser props since we now use local variables from polling
	export let focusedTicketId: number | null = null;
	export let showMessageInput: boolean = true; // Default to true for backward compatibility
	export let isReadOnly: boolean = false;

	let messages: Message[] = [];
	let loading = true;
	let connected = false;
	let customerName = '';
	let channelName = '';
	let previousPlatformId: number | null = null;
	let previousCustomerId: number | null = null;

	let loadingMore = false;
	let hasMore = false;
	let error: string | null = null;

	// Ticket data state - moved from ConversationHeader
	let ticket: any = null;
	let loginUser: any = $page.data.id;
	let ticketLoading = true;
	let ticketError: string | null = null;
	let backendTicketId: string | null = null;

	// Track previous owner ID for ownership change detection
	let previousOwnerId: number | null = null;

	// Polling service for ticket data
	let ticketPollingService: PollingService | null = null;
	let isTicketPollingEnabled = false;

	// Use local variables that are updated by polling instead of props
	// ticket.owner_id contains the current ticket owner ID
	$: canSendMessage = (ticket?.owner_id === loginUser) && (ticket?.status.toLowerCase() !== 'pending_to_close') && (ticket?.status.toLowerCase() !== 'closed');
	$: messageInputDisabled = loading || !canSendMessage;

	// Debug logging to track the values used in canSendMessage calculation
	// $: if (ticket || loginUser) {
	// 	console.log('ConversationView.svelte: canSendMessage calculation:', {
	// 		ticketOwnerId: ticket?.owner_id,
	// 		loginUserId: loginUser,
	// 		canSendMessage,
	// 		messageInputDisabled,
	// 		timestamp: new Date().toISOString()
	// 	});
	// }

	// Subscribe to conversation store
	$: platformData = $conversationStore;
	$: messages = platformData.messages.get(platformId) || [];
	$: hasMore = platformData.hasMore.get(platformId) || false;
	$: loadingMore = platformData.loadingStates.get(platformId) || false;

	// React to platformId or customerId changes
	$: if (platformId && customerId && (platformId !== previousPlatformId || customerId !== previousCustomerId)) {
		previousPlatformId = platformId;
		previousCustomerId = customerId;
		loadConversationForPlatform(customerId, platformId);
	}

	// Reactive mechanism to automatically mark messages as read when ticket ownership changes to current user
	$: if (ticket?.owner_id !== undefined && ticket.owner_id !== previousOwnerId) {
		// Check if ownership changed TO the current user (not away from them)
		if (ticket.owner_id === loginUser && previousOwnerId !== null) {
			// console.log('ConversationView: Ticket ownership changed to current user, auto-marking messages as read:', {
			// 	previousOwnerId,
			// 	newOwnerId: ticket.owner_id,
			// 	loginUser,
			// 	canSendMessage,
			// 	timestamp: new Date().toISOString()
			// });

			// Get unread messages from customers and mark them as read
			// Follow the same validation pattern as existing code
			const unreadMessageIds = messages
				.filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
				.map((msg: Message) => msg.id);

			if (canSendMessage && unreadMessageIds.length > 0) {
				// console.log('ConversationView: Auto-marking unread customer messages as read:', unreadMessageIds);
				markMessagesAsRead(unreadMessageIds);
			}

			// For remaining unread messages from System, mark them as read
			if (canSendMessage) {
				// console.log('ConversationView: Auto-marking all system messages as read');
				markAllMessagesAsRead();
			}
		}

		// Update previous owner ID for next comparison
		previousOwnerId = ticket.owner_id;
	}

	// React to ticket-related prop changes and fetch ticket data
	// This reactive statement will trigger when any of these props change:
	// customerId, platformId, ticketId, or access_token
	$: if (customerId && (platformId || ticketId) && access_token) {
		// console.log('ConversationView.svelte: Ticket-related props changed, fetching ticket data...', {
		// 	customerId,
		// 	platformId,
		// 	ticketId,
		// 	hasAccessToken: !!access_token,
		// 	timestamp: new Date().toISOString()
		// });
		fetchTicketData(customerId, platformId, access_token);
	}

	// Additional reactive statement to specifically track ticketId changes
	// $: if (ticketId !== undefined) {
	// 	console.log('ConversationView.svelte: ticketId prop changed:', {
	// 		newTicketId: ticketId,
	// 		timestamp: new Date().toISOString()
	// 	});
	// }

	/**
	 * Fetch ticket data - moved from ConversationHeader.svelte
	 */
	async function fetchTicketData(customerId: number, platformId: any, accessToken: string) {
		try {
			ticketLoading = true;
			ticketError = null;

			// If ticketId is provided, use it directly
			if (ticketId) {
				backendTicketId = ticketId.toString();
			} else {
				// Otherwise, get it from platform info
				const platformInfo = await services.customers.getPlatformInfo(
					customerId,
					platformId,
					accessToken
				);
				backendTicketId = platformInfo.id.toString();
			}

			// Get ticket information using the backendTicketId
			const response_ticket = await services.tickets.getById(backendTicketId, accessToken);
			// const response_selfUserInfo = await services.users.getUserInfo(accessToken);

			ticket = response_ticket.tickets;
			// loginUser = response_selfUserInfo.users;

			// console.log('ConversationView.svelte: fetchTicketData(): Updated Ticket Information:', ticket);

			// Restart polling when ticket data changes (e.g., when ticketId changes)
			// This ensures polling uses the correct ticket ID
			if (isTicketPollingEnabled) {
				// Clean up existing polling and restart with new ticket data
				cleanupTicketPolling();
				initializeTicketPolling();
			} else {
				// Initialize polling for the first time
				initializeTicketPolling();
			}
		} catch (err) {
			ticketError = err instanceof Error ? err.message : 'Failed to fetch ticket';
			console.error('Error fetching ticket:', err);
			ticket = null;
			// loginUser = null;
		} finally {
			ticketLoading = false;
		}
	}

	/**
	 * Custom fetcher function for polling service - moved from ConversationHeader.svelte
	 */
	async function fetchTicketPollingData(): Promise<{ ticket: any; loginUser: any }> {
		try {
			// Determine the backend ticket ID
			let currentBackendTicketId = backendTicketId;

			if (ticketId) {
				currentBackendTicketId = ticketId.toString();
			} else if (customerId && platformId && access_token) {
				const platformInfo = await services.customers.getPlatformInfo(
					customerId,
					platformId,
					access_token
				);
				currentBackendTicketId = platformInfo.id.toString();
			}

			if (!currentBackendTicketId) {
				throw new Error('No ticket ID available for fetching data');
			}

			// Fetch ticket and user data in parallel
			// const [response_ticket, response_selfUserInfo] = await Promise.all([
			// 	services.tickets.getById(currentBackendTicketId, access_token),
			// 	services.users.getUserInfo(access_token)
			// ]);
			const [response_ticket] = await Promise.all([
				services.tickets.getById(currentBackendTicketId, access_token)
			]);

			return {
				ticket: response_ticket.tickets,
				loginUser: loginUser
			};
		} catch (err) {
			console.error('ConversationView.svelte: fetchTicketPollingData(): Error:', err);
			throw err;
		}
	}

	/**
	 * Handle ticket polling data updates
	 */
	function handleTicketPollingDataUpdate(data: { ticket: any; loginUser: any }): void {
		try {
			ticket = data.ticket;
			// loginUser = data.loginUser;

			// Clear any existing errors since we got fresh data
			ticketError = null;

			// console.log('ConversationView.svelte: Ticket polling data updated successfully', data);
		} catch (err) {
			console.error('ConversationView.svelte: Error handling ticket polling data update:', err);
		}
	}

	/**
	 * Handle ticket polling errors
	 */
	function handleTicketPollingError(error: any): void {
		console.error('ConversationView.svelte: Ticket polling error:', error);
		ticketError = error instanceof Error ? error.message : 'Polling error occurred';
	}

	/**
	 * Initialize ticket polling service
	 */
	function initializeTicketPolling(): void {
		try {
			ticketPollingService = PollingService.getInstance();

			const pollingConfig: PollingConfig = {
				interval: 3000, // 3 seconds - matches other components
				customFetcher: fetchTicketPollingData,
				onDataChange: handleTicketPollingDataUpdate,
				onError: handleTicketPollingError,
				debugMode: false // Set to true for debugging
			};

			const success = ticketPollingService.registerEndpoint('conversation-view-ticket', pollingConfig);

			if (success) {
				isTicketPollingEnabled = true;
				// console.log('ConversationView.svelte: Ticket polling started successfully');
			} else {
				console.error('ConversationView.svelte: Failed to start ticket polling');
			}
		} catch (err) {
			console.error('ConversationView.svelte: Error initializing ticket polling:', err);
		}
	}

	/**
	 * Cleanup ticket polling
	 */
	function cleanupTicketPolling(): void {
		if (ticketPollingService && isTicketPollingEnabled) {
			ticketPollingService.unregisterEndpoint('conversation-view-ticket');
			isTicketPollingEnabled = false;
			// console.log('ConversationView.svelte: Ticket polling cleaned up');
		}
	}

	// Set up event listeners for real-time WebSocket message handling
	onMount(() => {
		if (typeof window !== 'undefined') {
			window.addEventListener('platform-new-message', handleNewMessage as EventListener);
		}
	});

	onDestroy(() => {
		// Cleanup event listeners
		if (typeof window !== 'undefined') {
			window.removeEventListener('platform-new-message', handleNewMessage as EventListener);
		}

		// Cleanup ticket polling
		cleanupTicketPolling();

		// Cleanup WebSocket connections
		disconnectWebSocket();

		// Clear conversation when component is destroyed
		if (platformId) {
			conversationStore.clearConversation(platformId);
		}
	});

	// async function loadConversationForPlatform(custId: number, platId: number) {
	// 	// Disconnect from previous WebSocket if any
	// 	disconnectWebSocket();

	// 	// Clear previous messages to show loading state
	// 	messages = [];

	// 	try {
	// 		loading = true;

	// 		// Load platform info
	// 		const platformResponse = await fetch(
	// 			`${getBackendUrl()}/customer/api/customers/${custId}/platform-identities/${platId}/`,
	// 			{
	// 				credentials: 'include'
	// 			}
	// 		);

	// 		if (platformResponse.ok) {
	// 			const platformData = await platformResponse.json();

	// 			// Handle both single result and array of results
	// 			const platform = Array.isArray(platformData.results)
	// 				? platformData.results[0]
	// 				: platformData;

	// 			customerName = platform.display_name || platform.platform_username || 'Unknown User';
	// 			channelName = platform.channel_name || platform.platform;
	// 		}

	// 		// Load messages using the store's built-in method
	// 		await conversationStore.loadConversation(custId, platId);

	// 		// Get the loaded messages to check for unread ones
	// 		const loadedMessages = platformMessages;

	// 		// Mark messages as read
	// 		const unreadMessageIds = loadedMessages
	// 			.filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
	// 			.map((msg: Message) => msg.id);

	// 		if (unreadMessageIds.length > 0) {
	// 			markMessagesAsRead(unreadMessageIds);
	// 		}

	// 		// Connect WebSocket for this platform
	// 		connectWebSocket(custId, platId);

	// 	} catch (error) {
	// 		console.error('Error loading conversation:', error);
	// 	} finally {
	// 		loading = false;
	// 	}
	// }

	async function loadConversationForPlatform(custId: number, platId: number) {
		// Reset error state
		error = null;

		// Disconnect from previous WebSocket if any
		disconnectWebSocket();

		// Clear previous conversation
		conversationStore.clearConversation(platId);

		try {
			loading = true;

			// Load platform info
			const platformResponse = await fetch(
				`${getBackendUrl()}/customer/api/customers/${custId}/platform-identities/${platId}/`,
				{
					credentials: 'include'
				}
			);

			if (platformResponse.ok) {
				const platformData = await platformResponse.json();

				// Handle both single result and array of results
				const platform = Array.isArray(platformData.results)
					? platformData.results[0]
					: platformData;

				if (platform) {
					customerName = platform.display_name || platform.platform_username || 'Unknown User';
					channelName = platform.channel_name || platform.platform;
				}
			} else {
				console.error('Failed to load platform info');
			}

			// Load messages using the store's built-in method
			await conversationStore.loadConversation(custId, platId);

			// Get the loaded messages to check for unread ones
			const loadedMessages = messages;

			// Mark messages as read
			const unreadMessageIds = loadedMessages
				.filter((msg: Message) => !msg.is_self && msg.status !== 'READ')
				.map((msg: Message) => msg.id);

			if (canSendMessage && unreadMessageIds.length > 0) {
				console.log('ConversationView: Marking unread messages as read:', unreadMessageIds);
				markMessagesAsRead(unreadMessageIds);
			}

			// Connect WebSocket for this platform
			connectWebSocket(custId, platId);
		} catch (err) {
			console.error('Error loading conversation:', err);
			error = 'Failed to load conversation. Please try again.';
		} finally {
			loading = false;
		}
	}

	function connectWebSocket(custId: number, platId: number) {
		// For the global platform WebSocket approach
		if (typeof window !== 'undefined') {
			// Subscribe to this specific platform for updates
			platformWebSocket.subscribeToPlatform(platId);
			connected = true;
		}
	}

	function disconnectWebSocket() {
		// Unsubscribe from the current platform if using global WebSocket
		if (platformId && typeof window !== 'undefined') {
			platformWebSocket.unsubscribeFromPlatform(platformId);
		}
		connected = false;
	}

	// async function markMessagesAsRead(messageIds: number[]) {
	// 	try {
	// 		await fetch(
	// 			`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/read/`,
	// 			{
	// 				method: 'POST',
	// 				credentials: 'include',
	// 				headers: {
	// 					'Content-Type': 'application/json',
	// 				},
	// 				body: JSON.stringify({
	// 					message_ids: messageIds
	// 				})
	// 			}
	// 		);
	// 	} catch (error) {
	// 		console.error('Error marking messages as read:', error);
	// 	}
	// }

	async function markMessagesAsRead(messageIds: number[]) {
		try {
			await fetch(
				`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/read/`,
				{
					method: 'POST',
					credentials: 'include',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						message_ids: messageIds
					})
				}
			);

			// Update message status in store
			messageIds.forEach((id) => {
				conversationStore.updateMessageStatus(platformId, id, 'READ');
			});
		} catch (error) {
			console.error('Error marking messages as read:', error);
		}
	}

	async function markAllMessagesAsRead() {
		try {
			await fetch(
				`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/readAll/`,
				{
					method: 'POST',
					credentials: 'include',
					headers: {
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({
						message_ids: []
					})
				}
			);
		} catch (error) {
			console.error('Error marking messages as read:', error);
		}
	}

	// async function handleSendMessage(event: CustomEvent<{ content: string; type: string }>) {
	// 	const { content, type } = event.detail;

	// 	try {
	// 		const response = await fetch(
	// 			`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/`,
	// 			{
	// 				credentials: 'include',
	// 				method: 'POST',
	// 				headers: {
	// 					'Content-Type': 'application/json',
	// 				},
	// 				body: JSON.stringify({
	// 					message: content,
	// 					message_type: type,
	// 				}),
	// 			}
	// 		);

	// 		if (response.ok) {
	// 			const newMessage = await response.json();
	// 			conversationStore.addMessage(platformId, newMessage);
	// 		}
	// 	} catch (error) {
	// 		console.error('Error sending message:', error);
	// 	}
	// }

	async function handleSendMessage(
		event: CustomEvent<{ content: string; type: string; files?: File[] }>
	) {

		// Early return if user cannot send messages
		if (!canSendMessage) {
			// console.log('User is not authorized to send messages for this ticket');
			return;
		}

		const { content, type, files } = event.detail;

		try {
			// Show loading state if needed
			const hasFiles = files && files.length > 0;

			// Use conversation service for cleaner code
			const response = await conversationService.sendMessage(
				customerId,
				platformId,
				content,
				type,
				files
			);

			if (response) {
				// Add message to store
				conversationStore.addMessage(platformId, response);

				// Scroll to bottom after sending message
				// This would be handled in MessageList component
			}
		} catch (error) {
			console.error('Error sending message:', error);
			// Show error notification
			// You could add a toast notification here
		}
	}

	// async function handleLoadMore() {
	// 	if (loading || !messages.length) return;

	// 	const oldestMessage = messages[0];
	// 	if (oldestMessage) {
	// 		try {
	// 			loading = true;
	// 			const response = await fetch(
	// 				`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/?before=${oldestMessage.id}&limit=50`,
	// 				{
	// 					credentials: 'include'
	// 				}
	// 			);

	// 			if (response.ok) {
	// 				const data = await response.json();
	// 				if (data.messages && data.messages.length > 0) {
	// 					// Prepend older messages
	// 					conversationStore.prependMessages(platformId, data.messages);
	// 				}
	// 			}
	// 		} catch (error) {
	// 			console.error('Error loading more messages:', error);
	// 		} finally {
	// 			loading = false;
	// 		}
	// 	}
	// }

	// async function handleLoadMore() {
	// 	if (loadingMore || !messages.length || !hasMore) return;

	// 	const oldestMessage = messages[0];
	// 	if (oldestMessage) {
	// 		try {
	// 			// Set loading state
	// 			conversationStore.update(state => {
	// 				state.loadingStates.set(platformId, true);
	// 				return { ...state, loadingStates: new Map(state.loadingStates) };
	// 			});

	// 			const response = await fetch(
	// 				`${getBackendUrl()}/customer/api/customers/${customerId}/platforms/${platformId}/messages/?before=${oldestMessage.id}&limit=50`,
	// 				{
	// 					credentials: 'include'
	// 				}
	// 			);

	// 			if (response.ok) {
	// 				const data = await response.json();
	// 				if (data.messages && data.messages.length > 0) {
	// 					// Fix: Pass hasMore parameter correctly
	// 					conversationStore.prependMessages(
	// 						platformId,
	// 						data.messages,
	// 						data.has_more || false
	// 					);
	// 				}
	// 			}
	// 		} catch (error) {
	// 			console.error('Error loading more messages:', error);
	// 		} finally {
	// 			// Clear loading state
	// 			conversationStore.update(state => {
	// 				state.loadingStates.set(platformId, false);
	// 				return { ...state, loadingStates: new Map(state.loadingStates) };
	// 			});
	// 		}
	// 	}
	// }

	async function handleLoadMore() {
		if (loadingMore || !messages.length || !hasMore) return;

		const oldestMessage = messages[0];
		if (oldestMessage) {
			try {
				// Use the existing loadMoreMessages method which handles loading state internally
				await conversationStore.loadMoreMessages(customerId, platformId, oldestMessage.id);
			} catch (error) {
				console.error('Error loading more messages:', error);
			}
		}
	}

	// // Handle real-time message updates
	// function handleNewMessage(event: CustomEvent) {
	// 	const { platformId: msgPlatformId, message } = event.detail;
	// 	if (msgPlatformId === platformId) {
	// 		// The message is already added to the store by the WebSocket handler

	// 		// Force update if needed
	// 		conversationStore.addMessage(platformId, message);

	// 		// We just need to mark it as read if appropriate
	// 		if (document.hasFocus() && !message.is_self && message.status !== 'READ') {
	// 			markMessagesAsRead([message.id]);
	// 		}
	// 	}
	// }

	// Handle real-time message updates from WebSocket
	function handleNewMessage(event: Event) {
		try {
			// Cast to CustomEvent to access detail property
			const customEvent = event as CustomEvent;
			const { platformId: msgPlatformId, message } = customEvent.detail;

			// Only process messages for the currently viewed platform
			if (msgPlatformId === platformId) {
				// console.log('ConversationView: Received WebSocket message for current platform:', {
				// 	platformId: msgPlatformId,
				// 	messageId: message.id,
				// 	isFromCustomer: !message.is_self,
				// 	currentStatus: message.status,
				// 	documentHasFocus: document.hasFocus()
				// });

				// Don't add message here - PlatformIdentityList already handles adding to store
				// Just mark as read if appropriate (user is viewing and message is from customer)
				if (canSendMessage && !message.is_self && message.status !== 'READ') {
					console.log('ConversationView: Auto-marking WebSocket message as read:', message.id);
					markMessagesAsRead([message.id]);
				}
			}
		} catch (error) {
			console.error('ConversationView: Error handling WebSocket message:', error);
		}
	}

	// function handleNewMessage(event: CustomEvent) {
	// 	const { platformId, message, unreadCount } = event.detail;

	// 	// Update UI elements
	// 	latestMessages.set(platformId, message);
	// 	unreadCounts.set(platformId, unreadCount);

	// 	// Only add to store if it's not the currently selected platform
	// 	// (ConversationView will handle the selected one)
	// 	if (platformId !== selectedPlatformId) {
	// 		conversationStore.addMessage(platformId, message);
	// 	}
	// }

	// $: console.info('Selected Customer:', customerId);
	// $: console.info('Platform Identities:', platformId);
</script>

<div class="flex h-full flex-col">
	<ConversationHeader
		{customerId}
		{customerName}
		{channelName}
		{connected}
		{platformId}
		{ticketId}
		{users}
		{priorities}
		{statuses}
		{topics}
		{access_token}
		{ticket}
		{loginUser}
		ticketLoading={ticketLoading}
		ticketError={ticketError}
		isReadOnly={isReadOnly}
	/>

	<!-- <MessageList 
		{messages}
		{loading}
		on:loadMore={handleLoadMore}
	/> -->

	<MessageList {messages} {loading} {hasMore} {focusedTicketId} on:loadMore={handleLoadMore} />

	{#if showMessageInput} <!-- Disable for monitoring id page -->
		<MessageInput
			on:send={handleSendMessage}
			disabled={messageInputDisabled}
			canSendMessage={canSendMessage}
			isNotTicketOwner={(ticket?.owner_id !== loginUser)}
			isTicketPendingToClose={(ticket?.status.toLowerCase() === 'pending_to_close')}
			isTicketClosed={(ticket?.status.toLowerCase() === 'closed')}
			conversationId={`${customerId}-${platformId}`}
		/>
	{/if}
</div>
