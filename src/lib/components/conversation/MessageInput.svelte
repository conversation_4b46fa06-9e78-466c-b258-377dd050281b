<script lang="ts">
	import { createEventDispatcher, onMount, onDestroy } from 'svelte';
	import { t } from '$lib/stores/i18n';
	import { templates as templatesStore, templateActions } from '$lib/stores/quickResponseTemplates';
	import { validateAndSanitizeMessage } from '$lib/utils/messageSanitizer';
	const dispatch = createEventDispatcher();

	export let disabled = false;
	export let canSendMessage = true;
	export let isNotTicketOwner = false;
	export let isTicketPendingToClose = false;
	export let isTicketClosed = false;
	export let conversationId: string | number | null = null; // Identifier for current conversation

	let messageText = '';
	let isTyping = false;
	let typingTimeout: number | null = null;
	let fileInput: HTMLInputElement;
	let selectedFiles: File[] = [];
	let showFilePreview = false;

	let showSuggestions = false;
	let filteredSuggestions: Array<{
		id: string;
		keyword: string;
		template: string;
		description: string;
	}> = [];
	let selectedSuggestionIndex = -1;
	let textareaElement: HTMLTextAreaElement;
	let suggestionDropdown: HTMLElement;
	let currentTriggerWord = '';
	let triggerStartPosition = -1;
	let isSending = false;

	// Validation state
	let validationError = '';
	$: hasValidationError = canSendMessage ? validationError.length > 0 : false;

	// Track previous conversation ID to detect changes
	let previousConversationId = conversationId;

	// Clear validation error and reset component state when conversation changes
	$: if (conversationId !== previousConversationId) {
		// Clear validation error state - this hides the red error bar immediately
		validationError = '';

		// Reset message input state for clean slate in new conversation
		messageText = '';
		selectedFiles = [];
		showFilePreview = false;

		// Hide suggestions dropdown to prevent showing suggestions from previous conversation
		hideSuggestions();

		// Stop typing indicator to clean up previous conversation state
		stopTyping();

		// Update tracking variable to prevent infinite reactive loop
		previousConversationId = conversationId;
	}

	$: responseTemplates = $templatesStore;

	onMount(async () => {
		await templateActions.loadTemplates();
	});

	function validateCurrentMessage() {
		if (!messageText.trim()) {
			validationError = '';
			return;
		}

		const result = validateAndSanitizeMessage(messageText);

		if (!result.isValid) {
			validationError = result.error;
			messageText = '';
		} else {
			validationError = '';
		}
	}

	function handleSend() {
		// Validate and sanitize message before sending
		const result = validateAndSanitizeMessage(messageText);

		if (!result.isValid) {
			validationError = result.error;
			return;
		}

		console.log('Sending message:', result.sanitizedContent, selectedFiles);

		if ((result.sanitizedContent || selectedFiles.length > 0) && !disabled && canSendMessage && !hasValidationError) {
			dispatch('send', {
				content: result.sanitizedContent,
				type: 'TEXT',
				files: selectedFiles
			});
			messageText = '';
			selectedFiles = [];
			showFilePreview = false;
			validationError = '';
			stopTyping();
		}
	}

	function handleKeyPress(event: KeyboardEvent) {
		if (disabled || !canSendMessage) {
			event.preventDefault();
			return;
		}

		if (event.key === 'Enter' && !event.shiftKey) {
			event.preventDefault();
			handleSend();
		}
	}

	function handleInput(event: Event) {
		if (disabled || !canSendMessage) {
			return;
		}

		if (!isTyping) {
			isTyping = true;
			dispatch('typing', { isTyping: true });
		}

		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
		}

		typingTimeout = window.setTimeout(() => {
			stopTyping();
		}, 1000);

		// Check for suggestions
		// checkForSuggestions();

		// Validate message content
		validateCurrentMessage();
	}

	function checkForSuggestions() {
		if (!textareaElement) return;

		const cursorPosition = textareaElement.selectionStart;
		const textBeforeCursor = messageText.substring(0, cursorPosition);

		// Find the last word before cursor
		const words = textBeforeCursor.split(/\s+/);
		const lastWord = words[words.length - 1].toLowerCase();

		if (lastWord.length >= 2) {
			// Find matching templates
			const matches = responseTemplates.filter(
				(template) =>
					template.keyword.toLowerCase().includes(lastWord) ||
					template.description.toLowerCase().includes(lastWord)
			);

			if (matches.length > 0) {
				filteredSuggestions = matches;
				currentTriggerWord = lastWord;
				triggerStartPosition = cursorPosition - lastWord.length;
				selectedSuggestionIndex = -1;
				showSuggestions = true;
			} else {
				hideSuggestions();
			}
		} else {
			hideSuggestions();
		}
	}

	function selectSuggestion(suggestion: {
		id: string;
		keyword: string;
		template: string;
		description: string;
	}) {
		if (!textareaElement) return;

		const cursorPosition = textareaElement.selectionStart;
		const beforeTrigger = messageText.substring(0, triggerStartPosition);
		const afterCursor = messageText.substring(cursorPosition);

		// Replace the trigger word with the template
		messageText = beforeTrigger + suggestion.template + afterCursor;

		// Hide suggestions first
		hideSuggestions();

		// Set cursor position after the inserted template and return focus to textarea
		setTimeout(() => {
			const newCursorPosition = beforeTrigger.length + suggestion.template.length;
			textareaElement.setSelectionRange(newCursorPosition, newCursorPosition);
			textareaElement.focus();
		}, 0);
	}

	function hideSuggestions() {
		showSuggestions = false;
		filteredSuggestions = [];
		selectedSuggestionIndex = -1;
		currentTriggerWord = '';
		triggerStartPosition = -1;
	}

	function hideSuggestionsAndReturnFocus() {
		hideSuggestions();
		// Return focus to textarea
		setTimeout(() => {
			if (textareaElement) {
				textareaElement.focus();
			}
		}, 0);
	}

	function scrollToSelectedSuggestion() {
		if (!suggestionDropdown || selectedSuggestionIndex < 0) return;

		const selectedElement = suggestionDropdown.children[selectedSuggestionIndex] as HTMLElement;
		if (selectedElement) {
			selectedElement.scrollIntoView({ block: 'nearest' });
		}
	}

	function handleClickOutside(event: MouseEvent) {
		if (
			suggestionDropdown &&
			!suggestionDropdown.contains(event.target as Node) &&
			!textareaElement.contains(event.target as Node)
		) {
			hideSuggestionsAndReturnFocus();
		}
	}

	function stopTyping() {
		if (isTyping) {
			isTyping = false;
			dispatch('typing', { isTyping: false });
		}
		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
			typingTimeout = null;
		}
	}

	function handleFileClick() {
		if (disabled || !canSendMessage) return;
		fileInput.click();
	}

	function handleFileSelect(event: Event) {
		if (disabled || !canSendMessage) return;
		
		const input = event.target as HTMLInputElement;
		if (input.files && input.files.length > 0) {
			selectedFiles = [...selectedFiles, ...Array.from(input.files)];
			showFilePreview = true;
			input.value = '';
		}
	}

	function removeFile(index: number) {
		selectedFiles = selectedFiles.filter((_, i) => i !== index);
		if (selectedFiles.length === 0) {
			showFilePreview = false;
		}
	}

	function formatFileSize(bytes: number): string {
		if (bytes === 0) return '0 Bytes';
		const k = 1024;
		const sizes = ['Bytes', 'KB', 'MB', 'GB'];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
	}

	onMount(() => {
		document.addEventListener('click', handleClickOutside);
	});

	onDestroy(() => {
		document.removeEventListener('click', handleClickOutside);
		if (typingTimeout) {
			window.clearTimeout(typingTimeout);
		}
	});

	// Handle paste events
	function handlePaste(event: ClipboardEvent) {
		if (disabled || !canSendMessage) {
			event.preventDefault();
			return;
		}

		event.preventDefault();

		const pastedText = event.clipboardData?.getData('text') || '';

		// Insert pasted text at cursor position
		const cursorPosition = textareaElement.selectionStart;
		const beforeCursor = messageText.substring(0, cursorPosition);
		const afterCursor = messageText.substring(textareaElement.selectionEnd);

		messageText = beforeCursor + pastedText + afterCursor;

		// Validate message content after paste
		validateCurrentMessage();

		// Set cursor position after pasted content
		setTimeout(() => {
			const newPosition = cursorPosition + pastedText.length;
			textareaElement.setSelectionRange(newPosition, newPosition);
			textareaElement.focus();
		}, 0);
	}
</script>

{#if !canSendMessage}
	<div class="border-t border-gray-200 bg-yellow-50 px-4 py-3">
		<div class="flex items-center">
			<svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
				<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
			</svg>
			<span class="ml-2 text-sm text-yellow-800">
				{#if isNotTicketOwner}
					{t('input_error_not_ticket_owner')}
				{:else if isTicketPendingToClose}
					<p>{t('input_error_ticket_pending_to_close')}</p>
					<p>{t('input_error_create_new_ticket')}</p>
				{:else if isTicketClosed}
					<p>{t('input_error_ticket_closed')}</p>
					<p>{t('input_error_create_new_ticket')}</p>
				{/if}
			</span>
		</div>
	</div>
{/if}

{#if hasValidationError && canSendMessage && !isNotTicketOwner && !isTicketPendingToClose && !isTicketClosed}
	<div class="border-t border-gray-200 bg-red-50 px-4 py-3">
		<div class="flex items-center">
			<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
				<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
			</svg>
			<span class="ml-2 text-sm text-red-800">
				{validationError}
			</span>
		</div>
	</div>
{/if}

{#if showFilePreview && selectedFiles.length > 0}
	<div class="border-t border-gray-200 bg-gray-50 px-4 py-3">
		<div class="mb-3 flex items-center justify-between">
			<span class="text-sm font-medium text-gray-700">ไฟล์จะส่ง:</span>
			<button
				id="message-input-clear-files-button"
				on:click={() => {
					selectedFiles = [];
					showFilePreview = false;
				}}
				class="text-sm text-red-600 hover:text-red-700"
				disabled={disabled || !canSendMessage}
			>
				ยก
			</button>
		</div>

		<div class="relative">
			{#each selectedFiles as file, index}
				<div
					class="relative mb-2 rounded-lg border bg-white p-3 shadow-sm last:mb-0"
					style="transform: translateY({index * -2}px) translateX({index *
						2}px); z-index: {selectedFiles.length - index};"
				>
					<div class="flex items-center justify-between">
						<div class="min-w-0 flex-1">
							<div class="truncate text-sm font-medium text-gray-900">{file.name}</div>
							<div class="text-xs text-gray-500">{formatFileSize(file.size)}</div>
						</div>
						<button
							id="message-input-remove-file-button-{index}"
							on:click={() => removeFile(index)}
							class="ml-2 rounded-full p-1 text-red-500 hover:bg-red-50 hover:text-red-700"
							title="ลบไฟล์"
							disabled={disabled || !canSendMessage}
						>
							<svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									stroke-linecap="round"
									stroke-linejoin="round"
									stroke-width="2"
									d="M6 18L18 6M6 6l12 12"
								/>
							</svg>
						</button>
					</div>
				</div>
			{/each}
		</div>
	</div>
{/if}

<div class="relative border-t border-gray-200 bg-white px-4 py-3 {!canSendMessage ? 'opacity-50' : ''}">
	<div class="flex items-center space-x-3">
		<button
			id="message-input-attachment-button"
			on:click={handleFileClick}
			class="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full transition-colors hover:bg-gray-100 {disabled || !canSendMessage ? 'cursor-not-allowed opacity-50' : ''}"
			title="แนบไฟล์"
			disabled={disabled || !canSendMessage}
		>
			<svg class="h-5 w-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13"
				/>
			</svg>
		</button>

		<div class="flex min-w-0 flex-1">
			{#if showSuggestions && filteredSuggestions.length > 0}
				<div
					id="message-input-suggestion-dropdown"
					bind:this={suggestionDropdown}
					class="suggestion-dropdown absolute bottom-full left-0 right-0 z-50 mb-2 max-h-60 overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg"
				>
					{#each filteredSuggestions as suggestion, index}
						<button
							id="message-input-suggestion-{index}"
							class="w-full border-b border-gray-100 px-4 py-3 text-left transition-all duration-150 last:border-b-0 hover:bg-gray-50 {index ===
							selectedSuggestionIndex
								? 'border-blue-200 bg-blue-50 ring-2 ring-blue-200 ring-opacity-50'
								: ''}"
							on:click={() => selectSuggestion(suggestion)}
							on:mouseenter={() => (selectedSuggestionIndex = index)}
						>
							<div class="flex items-start justify-between">
								<div class="min-w-0 flex-1">
									<div class="truncate text-sm font-medium text-gray-900">
										{suggestion.keyword}
									</div>
								</div>
								<div class="ml-2 flex-shrink-0">
									<span
										class="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800"
									>
										Template
									</span>
								</div>
							</div>
							<div class="mt-2 line-clamp-2 text-xs text-gray-600">
								{suggestion.template}
							</div>
						</button>
					{/each}
				</div>
			{/if}

			<textarea
				id="message-input-textarea"
				bind:this={textareaElement}
				bind:value={messageText}
				on:keydown={handleKeyPress}
				on:input={handleInput}
				on:paste={handlePaste}
				placeholder={canSendMessage ? t('type_message') : t('cannot_send_ticket_message')}
				rows="1"
				class="w-full resize-none rounded-lg border border-gray-300 px-3 py-2 text-sm focus:border-transparent focus:outline-none focus:ring-2 focus:ring-blue-500 {disabled || !canSendMessage ? 'cursor-not-allowed bg-gray-100' : ''}"
				style="min-height: 40px; max-height: 120px;"
				disabled={disabled || !canSendMessage}
				readonly={disabled || !canSendMessage}
				maxlength="200"
			/>
		</div>

		<button
			id="message-input-send-button"
			on:click={handleSend}
			disabled={disabled || !canSendMessage || (!messageText.trim() && selectedFiles.length === 0) || hasValidationError}
			class="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full transition-colors {messageText.trim() ||
			selectedFiles.length > 0
				? canSendMessage && !disabled && !hasValidationError
					? 'bg-blue-500 text-white shadow-sm hover:bg-blue-600'
					: 'cursor-not-allowed bg-gray-100 text-gray-400'
				: 'cursor-not-allowed bg-gray-100 text-gray-400'}"
			title="ส่งข้อความ"
		>
			<svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
				<path
					stroke-linecap="round"
					stroke-linejoin="round"
					stroke-width="2"
					d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"
				/>
			</svg>
		</button>
	</div>
</div>

<input
	id="message-input-file-input"
	bind:this={fileInput}
	type="file"
	multiple
	on:change={handleFileSelect}
	class="hidden"
	accept="*/*"
	disabled={disabled || !canSendMessage}
/>

<style>
	textarea {
		overflow-y: auto;
		line-height: 1.5;
	}

	textarea:focus {
		resize: none;
	}

	.relative {
		min-height: 60px;
	}

	.line-clamp-2 {
		display: -webkit-box;
		-webkit-line-clamp: 2;
		line-clamp: 2;
		-webkit-box-orient: vertical;
		overflow: hidden;
	}

	.suggestion-dropdown {
		animation: slideUp 0.2s ease-out;
	}

	@keyframes slideUp {
		from {
			opacity: 0;
			transform: translateY(10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.suggestion-dropdown::-webkit-scrollbar {
		width: 6px;
	}

	.suggestion-dropdown::-webkit-scrollbar-track {
		background: #f1f1f1;
		border-radius: 3px;
	}

	.suggestion-dropdown::-webkit-scrollbar-thumb {
		background: #c1c1c1;
		border-radius: 3px;
	}

	.suggestion-dropdown::-webkit-scrollbar-thumb:hover {
		background: #a8a8a8;
	}

	.suggestion-dropdown button:focus {
		outline: none;
	}

	.suggestion-dropdown button {
		transition: all 0.15s ease-in-out;
	}

	.suggestion-dropdown button.selected {
		background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
		border-color: #3b82f6;
		box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
		transform: translateY(-1px);
	}
</style>
