<!-- LineChart.svelte -->
<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import {
        Chart,
        registerables,
        type Chart as ChartJsInstance,
        type ChartData,
        type ChartOptions
    } from 'chart.js';
    import ChartDataLabels from 'chartjs-plugin-datalabels';

    // Import shared utilities
    import { createLineHoverPlugin } from '$lib/utils/chartPlugins';

    // Register all Chart.js components globally
    Chart.register(...registerables, ChartDataLabels);

    // Create the plugin instance
    const lineHoverPlugin = createLineHoverPlugin();

    // Define the interface for a generic single-line chart data item
    interface LineChartDataItem {
        label: string;
        value: number;
    }

    // Component props
    export let data: LineChartDataItem[] = [];
    export let chartLabel: string = 'Value';
    export let lineColor: string = '#3B82F6';
    export let showDataLabels: boolean = false;
    export let showLegend: boolean = true;

    // Component state
    let chartEl: HTMLCanvasElement;
    let chart: ChartJsInstance<'line'> | null = null;
    let isLoading: boolean = true;
    let error: string = '';
    let initialized: boolean = false;

    // Validation function
    function validateData(items: LineChartDataItem[]): boolean {
        if (!Array.isArray(items) || items.length === 0) return false;
        return items.every(item => 
            typeof item.label === 'string' && 
            item.label.length > 0 && 
            typeof item.value === 'number' && 
            !isNaN(item.value)
        );
    }

    // Create chart configuration
    function createChartConfig(): { data: ChartData<'line'>; options: ChartOptions<'line'> } {
        if (!validateData(data)) {
            return {
                data: { labels: [], datasets: [] },
                options: {}
            };
        }

        const labels = data.map(item => item.label);
        const values = data.map(item => item.value);
        const maxValue = Math.max(...values);

        const chartData: ChartData<'line'> = {
            labels: labels,
            datasets: [{
                label: chartLabel,
                data: values,
                borderColor: lineColor,
                backgroundColor: lineColor,
                fill: false,
                tension: 0.4,
                pointRadius: 3,
                pointBackgroundColor: lineColor,
                pointBorderColor: '#fff',
                pointHoverRadius: 5
            }]
        };

        const chartOptions: ChartOptions<'line'> = {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    grid: {
                        display: true,
                        color: 'rgba(200, 200, 200, 0.4)'
                    },
                    ticks: {
                        font: { size: 14 }
                    }
                },
                y: {
                    beginAtZero: true,
                    grid: {
                        display: true,
                        color: 'rgba(200, 200, 200, 0.4)'
                    },
                    ticks: {
                        font: { size: 14 }
                    },
                    suggestedMax: maxValue * 1.1
                }
            },
            plugins: {
                legend: {
                    display: showLegend,
                    position: 'bottom',
                    labels: {
                        font: { size: 14 }
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                },
                datalabels: {
                    display: showDataLabels ? 'auto' : false,
                    align: 'top',
                    anchor: 'center',
                    offset: 10,
                    formatter: (value) => value,
                    font: { size: 14 }
                }
            }
        };

        return { data: chartData, options: chartOptions };
    }

    // Initialize chart
    function initChart() {
        if (!chartEl || !validateData(data)) {
            error = 'Invalid data or canvas element not ready';
            isLoading = false;
            return;
        }

        try {
            const config = createChartConfig();
            
            chart = new Chart(chartEl, {
                type: 'line',
                data: config.data,
                options: config.options,
                plugins: [lineHoverPlugin]
            });

            initialized = true;
            isLoading = false;
            error = '';
            console.log('LineChart initialized successfully');
        } catch (err) {
            error = `Failed to initialize chart: ${err}`;
            isLoading = false;
            console.error('LineChart initialization failed:', err);
        }
    }

    // Update chart
    function updateChart() {
        if (!chart || !initialized) return;

        try {
            const config = createChartConfig();
            chart.data = config.data;
            chart.options = config.options;
            chart.update();
        } catch (err) {
            console.error('Failed to update chart:', err);
        }
    }

    // Destroy chart
    function destroyChart() {
        if (chart) {
            chart.destroy();
            chart = null;
            initialized = false;
        }
    }

    // Reset and retry
    function retry() {
        destroyChart();
        error = '';
        isLoading = true;
        initChart();
    }

    // Reactive statements
    $: if (chartEl && !initialized && validateData(data)) {
        initChart();
    }

    $: if (initialized && chart) {
        updateChart();
    }

    // Handle data changes
    $: if (data) {
        if (!validateData(data)) {
            error = 'Invalid or empty data provided';
            isLoading = false;
        } else if (initialized) {
            updateChart();
        }
    }

    onMount(() => {
        console.log('LineChart mounted with data:', data);
    });

    onDestroy(() => {
        destroyChart();
    });
</script>

<div class="chart-container">
    <canvas bind:this={chartEl} aria-label="Line chart"></canvas>

    {#if isLoading}
        <div class="status-overlay loading-overlay">
            <div class="spinner"></div>
            <p>Loading chart...</p>
        </div>
    {:else if error}
        <div class="status-overlay error-overlay">
            <p class="error-message">{error}</p>
            <button on:click={retry}>Retry</button>
        </div>
    {/if}
</div>

<style>
    .chart-container {
        position: relative;
        width: 100%;
        height: 400px;
    }

    .status-overlay {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        background-color: rgba(255, 255, 255, 0.9);
        z-index: 10;
    }

    .loading-overlay {
        color: #666;
    }

    .error-overlay {
        color: #d32f2f;
    }

    .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin-bottom: 10px;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .error-message {
        margin-bottom: 10px;
        font-weight: bold;
    }

    button {
        padding: 8px 16px;
        background-color: #3498db;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
    }

    button:hover {
        background-color: #2980b9;
    }
</style>