<script lang="ts">
    import { onMount, onDestroy } from 'svelte';
    import TicketList from './TicketList.svelte';
    import ConversationView from '$lib/components/conversation/ConversationView.svelte';
    import CustomerInfoPanel from '$lib/components/customer/CustomerInfoPanel.svelte';
    import { platformWebSocketBrowser } from '$lib/websocket/platformWebSocketBrowser';
    import { browser } from '$app/environment';
    import type { Customer } from '$lib/types/customer';
    import { getBackendUrl } from '$src/lib/config';
    import { t } from '$lib/stores/i18n';
    import type { PageData } from './$types';

    export let data: PageData;
    $: ({
        ticketId,
        ticket,
        ticket_messages,
        customer_policyholders,
        users,
        statuses,
        priorities,
        customer_notes,
        ticket_summaries,
        ticket_topics,
        ticket_owners,
        loginUser,
        access_token,
        // Get the customer and platform information from the ticket
        customer,
        platformId,
        customerTickets,
        hasMoreTickets
    } = data);

    let loading = false;
    let ticketPage = 1;
    let loadingMoreTickets = false;

    // Initialize selectedTicketId directly from data (ticket.id from URL)
    $: selectedTicketId = ticket?.id || null;
    $: selectedCustomer = customer || null;
    
    // Debug logging for selectedTicketId
    $: if (selectedTicketId && typeof window !== 'undefined') {
        console.log('Selected Ticket ID:', selectedTicketId);
    }

    // Initialize with the current ticket
    onMount(() => {
        if (!browser) return;
        
        console.log('DATA: ', data);
        if (ticket && ticket.id) {
            // Debug logging
            console.log('Monitoring page loaded with:', {
                ticketId: selectedTicketId,
                customerId: selectedCustomer?.customer_id,
                platformId: platformId,
                hasCustomerTickets: customerTickets?.length > 0
            });
            
            // Connect WebSocket for real-time updates
            platformWebSocketBrowser.connect();
            
            // Subscribe to the platform for updates
            if (platformId) {
                platformWebSocketBrowser.subscribeToPlatform(platformId);
            }
            
            // Programmatically trigger scroll to highlighted ticket after DOM is ready
            setTimeout(() => {
                if (selectedTicketId) {
                    navigateToTicket(selectedTicketId);
                }
            }, 500);
        }
    });

    onDestroy(() => {
        if (!browser) return;
        if (platformId) {
            platformWebSocketBrowser.unsubscribeFromPlatform(platformId);
        }
        platformWebSocketBrowser.disconnect();
    });

    async function handleTicketSelect(event: CustomEvent<{ ticketId: number }>) {
        const { ticketId: newTicketId } = event.detail;
        
        selectedTicketId = newTicketId;
        
        // Navigate to ticket in conversation view
        await navigateToTicket(newTicketId);
        
        // Update the URL without reloading the page
        const url = new URL(window.location.href);
        url.pathname = `/monitoring/${newTicketId}`;
        window.history.pushState({}, '', url);
    }

    async function handleLoadMoreTickets() {
        if (loadingMoreTickets || !hasMoreTickets) return;
        
        loadingMoreTickets = true;
        ticketPage += 1;
        
        try {
            const response = await fetch(`${getBackendUrl()}/customer/api/customers/${customer.customer_id}/platforms/${platformId}/tickets/?limit=7&page=${ticketPage}`, {
                credentials: 'include'
            });
            
            if (response.ok) {
                const newTicketsData = await response.json();
                customerTickets = [...customerTickets, ...newTicketsData.tickets];
                hasMoreTickets = newTicketsData.has_more;
            }
        } catch (error) {
            console.error('Error loading more tickets:', error);
        } finally {
            loadingMoreTickets = false;
        }
    }

    async function navigateToTicket(ticketId: number) {
        console.log('Navigating to ticket:', ticketId);
        
        // Dispatch event to conversation view to scroll to ticket
        const conversationEvent = new CustomEvent('navigate-to-ticket', {
            detail: { ticketId },
            bubbles: true,  // Allow event to bubble up
            composed: true  // Allow event to cross shadow DOM boundary
        });
        
        if (typeof window !== 'undefined') {
            window.dispatchEvent(conversationEvent);
        }
    }

    // async function loadTicketData(ticketId: number) {
    //     try {
    //         loading = true;
    //         const response = await fetch(`${getBackendUrl()}/ticket/api/tickets/${ticketId}/`, {
    //             credentials: 'include'
    //         });

    //         if (response.ok) {
    //             const ticketData = await response.json();
    //             // Update the ticket data as needed
    //             // This would typically trigger a reactivity update
    //         }
    //     } catch (error) {
    //         console.error('Error loading ticket data:', error);
    //     } finally {
    //         loading = false;
    //     }
    // }
</script>

<svelte:head>
    <title>{t('ticket_details')}</title>
</svelte:head>

<div class="flex h-screen bg-gray-100">
    <!-- Left Panel: Ticket List -->
    <div class="flex w-1/4 min-w-[400px] flex-col border-r border-gray-200 bg-white">
        <TicketList
            tickets={customerTickets}
            selectedTicketId={selectedTicketId}
            customerName={customer?.name || 'Unknown Customer'}
            hasMore={hasMoreTickets}
            loading={loadingMoreTickets}
            on:select={handleTicketSelect}
            on:loadMore={handleLoadMoreTickets}
        />
    </div>

    <!-- Middle Panel: Conversation -->
    <div class="flex flex-1 flex-col bg-white">
        {#if selectedTicketId && selectedCustomer && platformId}
            <ConversationView
                customerId={selectedCustomer.customer_id}
                platformId={platformId}
                ticketId={selectedTicketId}
                users={users}
                priorities={priorities}
                statuses={statuses}
                topics={ticket_topics}
                access_token={access_token}
                latest_ticket_owner_id={ticket.owner?.id}
                currentLoginUser={loginUser}
                focusedTicketId={selectedTicketId}
                showMessageInput={false}
                isReadOnly={true}
            />
        {:else}
            <div class="flex flex-1 items-center justify-center text-gray-500">
                <div class="text-center">
                    <svg
                        class="mx-auto mb-4 h-12 w-12 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"
                        />
                    </svg>
                    <p class="text-lg font-medium">{t('select_ticket')}</p>
                    <p class="mt-1 text-sm text-gray-500">{t('choose_ticket_to_view')}</p>
                </div>
            </div>
        {/if}
    </div>

    <!-- Right Panel: Customer Info -->
    <!-- <div class="w-84 bg-white border-l border-gray-200"> -->
    <div class="flex w-1/4 min-w-[400px] flex-col overflow-hidden border-l border-gray-200 bg-white">
        {#if selectedCustomer && platformId}
            <CustomerInfoPanel
                customer={selectedCustomer}
                platformId={platformId}
                access_token={access_token}
                hideAITab={true}
            />
        {:else}
            <div class="p-6 text-center text-gray-500">
                <p>{t('select_ticket_view_details')}</p>
            </div>
        {/if}
    </div>
</div>

<!-- <style>
    .scrollbar-stable {
        scrollbar-gutter: stable;
    }
    
    /* Style the scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
    }
    
    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
        background: #a1a1a1;
    }
    
    /* Prevent text selection while dragging */
    body.dragging {
        user-select: none;
    }
</style> -->