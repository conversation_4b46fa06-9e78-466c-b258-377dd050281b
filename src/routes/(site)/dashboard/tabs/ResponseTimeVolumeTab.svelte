<script lang="ts">
    import LineChart from '$lib/components/dashboard/LineChart.svelte';
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import ScoreCard from '$lib/components/dashboard/ScoreCard.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';
    import { t, dict } from '$lib/stores/i18n';
    import { onMount } from 'svelte';
    import thLocaleData from '$lib/locales/th.json';

    // Define API Base URL
    const API_BASE_URL: string = import.meta.env.VITE_PUBLIC_API_BASE_URL || 'https://backend.salmate-staging.aibrainlab.co/dashboard/api/';

    // Local state for filters
    let timeRange: string = 'Last 7 Days';
    let startDateCustom: string = '';
    let endDateCustom: string = '';

    // Data state
    let totalIncomingMessages: number | null = null;
    let totalIncomingMessagesTrend: number | null = null;
    let totalIncomingTickets: number | null = null;
    let totalIncomingTicketsTrend: number | null = null;
    let dailyIncomingChatVolume: LineChartData[] = [];
    let incomingMessagesByTimeSlot: TimeSlotDataItem[] = [];

    // Loading and error states
    let isLoadingDailyVolume: boolean = true;
    let dailyVolumeError: string | null = null;
    let isLoadingMessagesByTimeSlot: boolean = true;
    let messagesByTimeSlotError: string | null = null;
    let isLoadingTotalTickets: boolean = true;
    let totalTicketsError: string | null = null;

    // State for expanded chart/table modals
    let isDailyVolumeExpanded: boolean = false;
    let isMessagesByTimeSlotExpanded: boolean = false;

    // Interfaces for API responses and component props
    interface LineChartData {
        label: string;
        value: number;
    }

    interface TimeSlotDataItem {
        time_slot: string;
        monday: number;
        tuesday: number;
        wednesday: number;
        thursday: number;
        friday: number;
        saturday: number;
        sunday: number;
    }

    // API Response Interfaces
    interface IncomingMessageCountTimeSeriesResponse {
        time: string;
        incoming_message_count: number;
    }

    interface IncomingMessageCountResponse {
        main_period: {
            start_date: string;
            end_date: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number }[];
        };
        comparison_period: {
            start_date: string;
            end_date: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number }[];
        };
        percentage_change: number | null;
        units: string;
    }

    interface TicketTotalCountResponse {
        main_period: {
            start_date: string;
            end_date: string;
            metric_value_name: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number | null }[];
            detailed_tickets: any[];
        };
        comparison_period: {
            start_date: string;
            end_date: string;
            metric_value_name: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number | null }[];
        };
        percentage_change: number | null;
        units: string;
    }

    // Sort state for the table
    let currentSort: { [key: string]: { column: string; direction: 'asc' | 'desc' | null } } = {
        incomingMessagesByTimeSlot: { column: 'time_slot', direction: 'asc' },
    };

    // Reactive variable to determine if the current language is Thai
    $: isThaiLocale = $dict === thLocaleData;

    function sortTable<T>(dataArray: T[], key: keyof T, tableName: string): T[] {
        const currentTableSort = currentSort[tableName] || { column: null, direction: null };
        let newDirection: 'asc' | 'desc' = 'asc';

        if (currentTableSort.column === key) {
            newDirection = currentTableSort.direction === 'asc' ? 'desc' : 'asc';
        }

        currentSort = { ...currentSort, [tableName]: { column: String(key), direction: newDirection } };

        return [...dataArray].sort((a, b) => {
            const aValue = a[key];
            const bValue = b[key];

            if (typeof aValue === 'string' && typeof bValue === 'string') {
                if (key === 'time_slot') {
                    const parseTime = (timeStr: string) => {
                        const [start, end] = timeStr.split('-').map(s => parseInt(s.split(':')[0]));
                        return start * 60 + (end === 0 ? 24 * 60 : end * 60);
                    };
                    const timeA = parseTime(aValue);
                    const timeB = parseTime(bValue);
                    return newDirection === 'asc' ? timeA - timeB : timeB - timeA;
                }
                return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
            }
            return 0;
        });
    }

    function applyInitialSort<T>(dataArray: T[], tableName: string): T[] {
        const sortState = currentSort[tableName];
        if (sortState && sortState.column) {
            return [...dataArray].sort((a, b) => {
                const key = sortState.column as keyof T;
                const aValue = a[key];
                const bValue = b[key];
                const direction = sortState.direction;
                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    if (key === 'time_slot') {
                        const parseTime = (timeStr: string) => {
                            const [start, end] = timeStr.split('-').map(s => parseInt(s.split(':')[0]));
                            return start * 60 + (end === 0 ? 24 * 60 : end * 60);
                        };
                        const timeA = parseTime(aValue);
                        const timeB = parseTime(bValue);
                        return direction === 'asc' ? timeA - timeB : timeB - timeA;
                    }
                    return direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
                } else if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return direction === 'asc' ? aValue - bValue : bValue - aValue;
                }
                return 0;
            });
        }
        return dataArray;
    }

    function formatDate(date: Date): string {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    function getDateRange(range: string, customStartDate: string, customEndDate: string): { startDate: string | undefined; endDate: string | undefined } {
        let startDate: string | undefined;
        let endDate: string | undefined;
        const today = new Date();

        if (range === 'Last 7 Days') {
            const sevenDaysAgo = new Date(today);
            sevenDaysAgo.setDate(today.getDate() - 7);
            startDate = formatDate(sevenDaysAgo);
            endDate = formatDate(today);
        } else if (range === 'Last 30 Days') {
            const thirtyDaysAgo = new Date(today);
            thirtyDaysAgo.setDate(today.getDate() - 30);
            startDate = formatDate(thirtyDaysAgo);
            endDate = formatDate(today);
        } else if (range === 'This Month') {
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            startDate = formatDate(firstDayOfMonth);
            endDate = formatDate(today);
        } else if (range === 'Last Month') {
            const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
            startDate = formatDate(firstDayOfLastMonth);
            endDate = formatDate(lastDayOfLastMonth);
        } else if (range === 'Custom') {
            startDate = customStartDate;
            endDate = customEndDate;
        }
        return { startDate, endDate };
    }

    /**
     * Resets the filter state variables to their default values.
     * This function is called when the "Clear Filters" button is clicked.
     */
    function clearFilters() {
        timeRange = 'Last 7 Days';
        startDateCustom = '';
        endDateCustom = '';
    }

    async function fetchData() {
        console.log("fetchData called for all dashboards/tables");
        const { startDate, endDate } = getDateRange(timeRange, startDateCustom, endDateCustom);
        console.log(`Current filter settings: timeRange='${timeRange}', startDate='${startDate}', endDate='${endDate}'`);

        const urlParams = new URLSearchParams();
        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);
        const query = urlParams.toString();

        // Set all loading states to true at the beginning
        isLoadingTotalTickets = true;
        isLoadingMessagesByTimeSlot = true;
        isLoadingDailyVolume = true;

        // Reset all error states
        totalTicketsError = null;
        messagesByTimeSlotError = null;
        dailyVolumeError = null;

        try {
            const [
                incomingMessageCountResponse,
                ticketTotalCountResponse,
                customerMessageHeatmapResponse,
                incomingMessageCountTimeSeriesResponse
            ] = await Promise.all([
                fetch(`${API_BASE_URL}incoming-message-count/?${query}`),
                fetch(`${API_BASE_URL}ticket-total-count/?${query}`),
                fetch(`${API_BASE_URL}customer-message-heatmap/?${query}`),
                fetch(`${API_BASE_URL}incoming-message-count-time-series/?${query}`)
            ]);

            // Process Incoming Message Count
            if (!incomingMessageCountResponse.ok) {
                const errorBody = await incomingMessageCountResponse.text();
                throw new Error(`HTTP error! status: ${incomingMessageCountResponse.status}, message: ${incomingMessageCountResponse.statusText || errorBody}`);
            }
            const incomingMessageCountData: IncomingMessageCountResponse = await incomingMessageCountResponse.json();
            if (incomingMessageCountData.main_period.metric_value === null || incomingMessageCountData.main_period.metric_value === undefined) {
                totalIncomingMessages = null;
                totalIncomingMessagesTrend = null;
            } else {
                totalIncomingMessages = incomingMessageCountData.main_period.metric_value;
                totalIncomingMessagesTrend = incomingMessageCountData.percentage_change ?? null;
            }

            // Process Total Tickets
            if (!ticketTotalCountResponse.ok) {
                const errorBody = await ticketTotalCountResponse.text();
                throw new Error(`HTTP error! status: ${ticketTotalCountResponse.status}, message: ${ticketTotalCountResponse.statusText || errorBody}`);
            }
            const ticketTotalCountData: TicketTotalCountResponse = await ticketTotalCountResponse.json();
            if (ticketTotalCountData.main_period.metric_value === null || ticketTotalCountData.main_period.metric_value === undefined) {
                totalIncomingTickets = null;
                totalIncomingTicketsTrend = null;
                totalTicketsError = 'No data available';
            } else {
                totalIncomingTickets = ticketTotalCountData.main_period.metric_value;
                totalIncomingTicketsTrend = ticketTotalCountData.percentage_change ?? null;
            }

            // Process Incoming Messages by Time Slot
            if (!customerMessageHeatmapResponse.ok) {
                const errorBody = await customerMessageHeatmapResponse.text();
                throw new Error(`HTTP error! status: ${customerMessageHeatmapResponse.status}, message: ${customerMessageHeatmapResponse.statusText || errorBody}`);
            }
            const customerMessageHeatmapData: TimeSlotDataItem[] = await customerMessageHeatmapResponse.json();
            if (!Array.isArray(customerMessageHeatmapData) || customerMessageHeatmapData.length === 0) {
                incomingMessagesByTimeSlot = [];
                messagesByTimeSlotError = 'No data available';
            } else {
                incomingMessagesByTimeSlot = applyInitialSort(customerMessageHeatmapData, 'incomingMessagesByTimeSlot');
            }

            // Process Daily Incoming Message Volume
            if (!incomingMessageCountTimeSeriesResponse.ok) {
                const errorBody = await incomingMessageCountTimeSeriesResponse.text();
                throw new Error(`HTTP error! status: ${incomingMessageCountTimeSeriesResponse.status}, message: ${incomingMessageCountTimeSeriesResponse.statusText || errorBody}`);
            }
            const incomingMessageCountTimeSeriesData: IncomingMessageCountTimeSeriesResponse[] = await incomingMessageCountTimeSeriesResponse.json();
            if (!Array.isArray(incomingMessageCountTimeSeriesData) || incomingMessageCountTimeSeriesData.length === 0) {
                dailyIncomingChatVolume = [];
                dailyVolumeError = 'No data available';
            } else {
                dailyIncomingChatVolume = incomingMessageCountTimeSeriesData.map(item => ({
                    label: new Date(item.time).toLocaleDateString(
                        isThaiLocale ? 'th-TH-u-ca-buddhist' : 'en-US',
                        { month: 'short', day: 'numeric'}
                    ),
                    value: item.incoming_message_count
                }));
            }

        } catch (error: any) {
            console.error('Error fetching dashboard data:', error);
            // Set error states for all components in case of a general fetch error
            totalTicketsError = 'No data available';
            messagesByTimeSlotError = 'No data available';
            dailyVolumeError = 'No data available';

            // Clear data for all components if there's an error
            totalIncomingMessages = null;
            totalIncomingMessagesTrend = null;
            totalIncomingTickets = null;
            totalIncomingTicketsTrend = null;
            dailyIncomingChatVolume = [];
            incomingMessagesByTimeSlot = [];
        } finally {
            // Set all loading states to false regardless of success or failure
            isLoadingTotalTickets = false;
            isLoadingMessagesByTimeSlot = false;
            isLoadingDailyVolume = false;
        }
    }

    function getMessageCountColorClass(count: number): string {
        if (count > 10) {
            return 'bg-green-600 text-white font-bold';
        } else if (count > 5) {
            return 'bg-green-400 text-gray-800';
        } else if (count > 0) {
            return 'bg-green-200 text-gray-700';
        } else {
            return 'bg-gray-100 text-gray-500';
        }
    }

    onMount(() => {
        fetchData();
    });

    // Reactively re-fetch data when filters or isThaiLocale change
    $: timeRange, startDateCustom, endDateCustom, isThaiLocale, fetchData();
</script>

{#if isDailyVolumeExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbResponseTimeVolume.dailyIncomingChatVolume')}</h3>
                <button on:click={() => isDailyVolumeExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full">
                {#if isLoadingDailyVolume}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if dailyVolumeError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <LineChart
                        data={dailyIncomingChatVolume}
                        chartLabel="Line"
                        lineColor={COLORS.green}
                        showDataLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isMessagesByTimeSlotExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbResponseTimeVolume.incomingMessagesByTimeSlot')}</h3>
                <button on:click={() => isMessagesByTimeSlotExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="overflow-auto flex-grow w-full">
                {#if isLoadingMessagesByTimeSlot}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if messagesByTimeSlotError}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'time_slot', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.timeSlot')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'time_slot'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'monday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.monday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'monday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'tuesday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.tuesday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'tuesday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'wednesday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.wednesday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'wednesday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'thursday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.thursday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'thursday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'friday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.friday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'friday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'saturday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.saturday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'saturday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'sunday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.sunday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'sunday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each incomingMessagesByTimeSlot as item}
                                <tr>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm font-medium text-gray-900">{item.time_slot}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.monday)}">{item.monday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.tuesday)}">{item.tuesday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.wednesday)}">{item.wednesday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.thursday)}">{item.thursday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.friday)}">{item.friday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.saturday)}">{item.saturday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.sunday)}">{item.sunday}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

<div class="flex flex-col gap-6">
    <div class="flex flex-col md:flex-row md:items-center justify-end gap-4 bg-white rounded-lg shadow-md p-4">
        <div class="flex flex-wrap items-center justify-start sm:justify-end gap-2 w-full md:w-auto">
            <label for="time-range-filter" class="text-gray-700 font-medium">{t('db.timeRange')}:</label>
            <select id="time-range-filter" bind:value={timeRange} class="border border-gray-300 rounded-md p-2 text-gray-700 w-full sm:w-auto">
                <option value="Last 7 Days">{t('db.last7Days')}</option>
                <option value="Last 30 Days">{t('db.last30Days')}</option>
                <option value="This Month">{t('db.thisMonth')}</option>
                <option value="Last Month">{t('db.lastMonth')}</option>
                <option value="Custom">{t('db.custom')}</option>
            </select>
            {#if timeRange === 'Custom'}
                <input type="date" bind:value={startDateCustom} class="border border-gray-300 rounded-md p-2 w-full sm:w-auto" />
                <input type="date" bind:value={endDateCustom} class="border border-gray-300 rounded-md p-2 w-full sm:w-auto" />
            {/if}
            <button
                on:click={clearFilters}
                class="bg-transparent hover:bg-gray-100 text-red-700 font-semibold py-2 px-4 border border-red-500 hover:border-transparent rounded-md text-sm mt-2 sm:mt-0 w-full sm:w-auto whitespace-nowrap flex-shrink-0" >
                {t('db.clearFilters')}
            </button>
            <!-- <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto whitespace-nowrap flex-shrink-0" >
                {t('db.downloadCsvFullPage')}
            </button> -->
        </div>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-5 gap-6">
        <div class="lg:col-span-3 flex flex-col gap-6">
            <div class="grid grid-cols-1 gap-6"> <div class="bg-white rounded-lg shadow-md p-6">
                    <ScoreCard
                        title={t('dbResponseTimeVolume.totalIncomingMessages')}
                        value={totalIncomingMessages}
                        valueColor="text-black-600"
                        trendValue={totalIncomingMessagesTrend}
                        trendUnit="%"
                    />
                </div>
                <div class="bg-white rounded-lg shadow-md p-6">
                    <ScoreCard
                        title={t('dbResponseTimeVolume.totalTickets')}
                        value={totalIncomingTickets}
                        valueColor="text-black-600"
                        trendValue={totalIncomingTicketsTrend}
                        trendUnit="%" />
                </div>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                    <h2 class="text-xl font-semibold text-gray-700">{t('dbResponseTimeVolume.dailyIncomingChatVolume')}</h2>
                    <div class="flex gap-2">
                        <button
                            on:click={() => isDailyVolumeExpanded = true}
                            class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto whitespace-nowrap flex-shrink-0" >
                            {t('db.expand')}
                        </button>
                        <button
                            class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto whitespace-nowrap flex-shrink-0" >
                            {t('db.downloadExcel')}
                        </button>
                    </div>
                </div>
                <div class="w-full h-[30rem] flex items-center justify-center">
                    {#if isLoadingDailyVolume}
                        <div class="flex flex-col items-center justify-center text-gray-600">
                            <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                        </div>
                    {:else if dailyVolumeError}
                        <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                            {t('db.noDataAvailable')}
                        </div>
                    {:else}
                        <LineChart
                            data={dailyIncomingChatVolume}
                            chartLabel="Line"
                            lineColor={COLORS.green}
                            showDataLabels={true}
                        />
                    {/if}
                </div>
            </div>
        </div>

        <div class="lg:col-span-2 bg-white p-6 rounded-lg shadow-md overflow-x-auto">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbResponseTimeVolume.incomingMessagesByTimeSlot')}</h2>
                <div class="flex gap-2">
                    <button
                        on:click={() => isMessagesByTimeSlotExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto whitespace-nowrap flex-shrink-0" >
                        {t('db.expand')}
                    </button>
                    <button
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm w-full sm:w-auto whitespace-nowrap flex-shrink-0" >
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="overflow-x-auto overflow-y-auto">
                {#if isLoadingMessagesByTimeSlot}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if messagesByTimeSlotError}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'time_slot', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.timeSlot')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'time_slot'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'monday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.monday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'monday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'tuesday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.tuesday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'tuesday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'wednesday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.wednesday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'wednesday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'thursday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.thursday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'thursday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'friday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.friday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'friday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'saturday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.saturday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'saturday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => incomingMessagesByTimeSlot = sortTable(incomingMessagesByTimeSlot, 'sunday', 'incomingMessagesByTimeSlot')}>
                                    {t('dbResponseTimeVolume.sunday')}
                                    {#if currentSort.incomingMessagesByTimeSlot.column === 'sunday'}
                                        {#if currentSort.incomingMessagesByTimeSlot.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each incomingMessagesByTimeSlot as item}
                                <tr>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm font-medium text-gray-900">{item.time_slot}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.monday)}">{item.monday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.tuesday)}">{item.tuesday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.wednesday)}">{item.wednesday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.thursday)}">{item.thursday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.friday)}">{item.friday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.saturday)}">{item.saturday}</td>
                                    <td class="px-3 py-1.5 whitespace-nowrap text-sm {getMessageCountColorClass(item.sunday)}">{item.sunday}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
</div>