<script lang="ts">
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import LineChart from '$lib/components/dashboard/LineChart.svelte';
    import ScoreCard from '$lib/components/dashboard/ScoreCard.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';
    import { t, dict } from '$lib/stores/i18n';
    import thLocaleData from '$lib/locales/th.json';
    import { onMount } from 'svelte';

    // Define API Base URL
    const API_BASE_URL: string = import.meta.env.VITE_PUBLIC_API_BASE_URL || 'https://backend.salmate-staging.aibrainlab.co/dashboard/api/';
    // console.log("API Base URL detected:", API_BASE_URL);

    // Local state for filters
    let timeRange: string = 'Last 7 Days';
    let startDateCustom: string = '';
    let endDateCustom: string = '';

    // Expanded state variables for modals
    let isAgentChatbotExpanded: boolean = false;
    let isDailyCsatExpanded: boolean = false;
    let isDailyFirstResponseTimeExpanded: boolean = false;
    let isDailyResponseTimeExpanded: boolean = false;
    let isOverallSentimentExpanded: boolean = false;
    let isDailySentimentExpanded: boolean = false;
    let isCaseTypeSentimentExpanded: boolean = false;

    // Loading and error states for API connections - these will still hold backend messages for internal logging
    let isLoadingAgentChatbot: boolean = true;
    let agentChatbotError: string | null = null;
    let isLoadingSentimentSummary: boolean = true;
    let sentimentSummaryError: string | null = null;
    let isLoadingDailySentiment: boolean = true;
    let dailySentimentError: string | null = null;
    let isLoadingCaseTypeSentiment: boolean = true;
    let caseTypeSentimentError: string | null = null;

    // Loading and error states for the CSAT Score and Line Chart
    let isLoadingCSAT: boolean = true;
    let csatError: string | null = null;
    let isLoadingAvgFirstResponseTime: boolean = true;
    let avgFirstResponseTimeError: string | null = null;
    let isLoadingAvgResponseTime: boolean = true;
    let avgResponseTimeError: string | null = null;

    // Reactive variable to determine if the current language is Thai
    $: isThaiLocale = $dict === thLocaleData;

    // Define interfaces for API responses (remain the same)
    interface ScoreAndTimeSeriesAPIResponse {
        main_period: {
            start_date: string;
            end_date: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number | null }[];
        };
        comparison_period: {
            start_date: string;
            end_date: string;
            metric_value: number | null;
            time_series_data: { date: string; value: number | null }[];
        };
        percentage_change: number | null;
        units: string;
    }

    interface ResponderResponseTimeAPIResponse {
        responder_type: 'agent' | 'chatbot';
        total_count: number;
        raw_avg: number;
    }

    interface SentimentAnalysisSummaryAPIResponse {
        time: string;
        positive: number;
        neutral: number;
        negative: number;
    }

    interface SentimentTimeSeriesAPIResponse {
        time: string;
        positive: number;
        neutral: number;
        negative: number;
    }

    interface SentimentByCaseTypeAPIResponse {
        case_type: string;
        positive: number;
        neutral: number;
        negative: number;
    }

    // Define interfaces for the formatted data for charts (remain the same)
    interface AgentChatbotComparisonDataItem {
        type: string;
        count: number;
    }

    interface OverallSentimentAmountItem {
        label: 'Positive' | 'Neutral' | 'Negative';
        value: number;
    }

    interface DailySentimentTimelineItem {
        label: string;
        positive: number;
        neutral: number;
        negative: number;
    }

    interface ProductSentimentAmountItem {
        product: string;
        positive: number;
        neutral: number;
        negative: number;
    }

    interface LineChartDataItem {
        label: string;
        value: number;
    }

    // Data fetched from backend for charts
    let agentChatbotComparisonData: AgentChatbotComparisonDataItem[] = [];
    let overallSentimentAmounts: OverallSentimentAmountItem[] = [];
    let overallSentimentColors: string[] = [];
    let dailySentimentTimeline: DailySentimentTimelineItem[] = [];
    let productSentimentAmounts: ProductSentimentAmountItem[] = [];

    // Data state variables for Scorecard and Line Charts
    // These are now union types to accept string 'No data available'
    let averageCSAT: number | string | null = null;
    let csatPercentageChange: number | null = null;
    let avgCSAT: LineChartDataItem[] = [];
    let averageFirstResponseTimeSeconds: number | string | null = null;
    let firstResponseTimePercentageChange: number | null = null;
    let avgFirstResponseTime: LineChartDataItem[] = [];
    let averageResponseTimeSeconds: number | string | null = null;
    let averageResponseTimePercentageChange: number | null = null;
    let avgResponseTime: LineChartDataItem[] = [];


    // Helper functions from AgentPerformanceTab.svelte to handle date formatting and ranges
    function formatDate(date: Date): string {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    function formatDateForChart(dateString: string): string {
        const date = new Date(dateString);
        return date.toLocaleDateString(
            isThaiLocale ? 'th-TH-u-ca-buddhist' : 'en-US',
            { month: 'short', day: 'numeric'}
        );
    }

    function getDateRange(range: string, customStartDate: string, customEndDate: string): { startDate: string | undefined; endDate: string | undefined } {
        let startDate: string | undefined;
        let endDate: string | undefined;
        const today = new Date();

        if (range === 'Last 7 Days') {
            const sevenDaysAgo = new Date(today);
            sevenDaysAgo.setDate(today.getDate() - 7);
            startDate = formatDate(sevenDaysAgo);
            endDate = formatDate(today);
        } else if (range === 'Last 30 Days') {
            const thirtyDaysAgo = new Date(today);
            thirtyDaysAgo.setDate(today.getDate() - 30);
            startDate = formatDate(thirtyDaysAgo);
            endDate = formatDate(today);
        } else if (range === 'This Month') {
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            startDate = formatDate(firstDayOfMonth);
            endDate = formatDate(today);
        } else if (range === 'Last Month') {
            const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
            startDate = formatDate(firstDayOfLastMonth);
            endDate = formatDate(lastDayOfLastMonth);
        } else if (range === 'Custom') {
            startDate = customStartDate;
            endDate = customEndDate;
        }
        return { startDate, endDate };
    }

    function clearFilters() {
        timeRange = 'Last 7 Days';
        startDateCustom = '';
        endDateCustom = '';
    }

    async function fetchData() {
        console.log("fetchData called for Work Quality tab");
        const { startDate, endDate } = getDateRange(timeRange, startDateCustom, endDateCustom);
        console.log("Selected date range:", { startDate, endDate });

        const urlParams = new URLSearchParams();
        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);
        const query = urlParams.toString();

        // Reset all loading and error states at the beginning
        isLoadingAgentChatbot = true;
        agentChatbotError = null;
        isLoadingSentimentSummary = true;
        sentimentSummaryError = null;
        isLoadingDailySentiment = true;
        dailySentimentError = null;
        isLoadingCaseTypeSentiment = true;
        caseTypeSentimentError = null;
        isLoadingCSAT = true;
        csatError = null;
        isLoadingAvgFirstResponseTime = true;
        avgFirstResponseTimeError = null;
        isLoadingAvgResponseTime = true;
        avgResponseTimeError = null;

        // Reset all data to empty/null before fetching
        agentChatbotComparisonData = [];
        overallSentimentAmounts = [];
        overallSentimentColors = [];
        dailySentimentTimeline = [];
        productSentimentAmounts = [];
        averageCSAT = null;
        csatPercentageChange = null;
        avgCSAT = [];
        averageFirstResponseTimeSeconds = null;
        firstResponseTimePercentageChange = null;
        avgFirstResponseTime = [];
        averageResponseTimeSeconds = null;
        averageResponseTimePercentageChange = null;
        avgResponseTime = [];

        try {
            const [
                csatResponse,
                firstResponseTimeResponse,
                averageResponseTimeResponse,
                responderResponseTimeResponse,
                sentimentSummaryResponse,
                sentimentTimeSeriesResponse,
                sentimentByCaseTypeResponse
            ] = await Promise.all([
                fetch(`${API_BASE_URL}csat-score-time-series/?${query}`),
                fetch(`${API_BASE_URL}first-response-time/?${query}`),
                fetch(`${API_BASE_URL}average-response-time/?${query}`),
                fetch(`${API_BASE_URL}responder-response-time/?${query}`),
                fetch(`${API_BASE_URL}sentiment-analysis-summary/?${query}`),
                fetch(`${API_BASE_URL}sentiment-analysis-time-series/?${query}`),
                fetch(`${API_BASE_URL}sentiment-analysis-by-case-type/?${query}`)
            ]);

            // Process CSAT Score and Daily CSAT (Time Series)
            if (!csatResponse.ok) {
                const errorBody = await csatResponse.text();
                throw new Error(`HTTP error! status: ${csatResponse.status}, message: ${csatResponse.statusText || errorBody}`);
            }
            const csatData: ScoreAndTimeSeriesAPIResponse = await csatResponse.json();
            console.log("Fetched CSAT Score data:", csatData);
            if (csatData && csatData.main_period) {
                averageCSAT = csatData.main_period.metric_value !== null ? parseFloat(csatData.main_period.metric_value.toFixed(1)) : null;
                csatPercentageChange = csatData.percentage_change !== null ? parseFloat(csatData.percentage_change.toFixed(1)) : null;
                avgCSAT = csatData.main_period.time_series_data.map(item => ({
                    label: formatDateForChart(item.date),
                    value: item.value !== null ? parseFloat(item.value.toFixed(1)) : 0
                }));
            } else {
                averageCSAT = t('db.noDataAvailable');
                csatPercentageChange = null;
                avgCSAT = [];
                csatError = 'No data available from backend for CSAT Score.';
            }

            // Process Average First Response Time (seconds)
            if (!firstResponseTimeResponse.ok) {
                const errorBody = await firstResponseTimeResponse.text();
                throw new Error(`HTTP error! status: ${firstResponseTimeResponse.status}, message: ${firstResponseTimeResponse.statusText || errorBody}`);
            }
            const firstResponseTimeData: ScoreAndTimeSeriesAPIResponse = await firstResponseTimeResponse.json();
            console.log("Fetched First Response Time data:", firstResponseTimeData);
            if (firstResponseTimeData && firstResponseTimeData.main_period) {
                averageFirstResponseTimeSeconds = firstResponseTimeData.main_period.metric_value !== null ? parseFloat(firstResponseTimeData.main_period.metric_value.toFixed(1)) : null;
                firstResponseTimePercentageChange = firstResponseTimeData.percentage_change !== null ? parseFloat(firstResponseTimeData.percentage_change.toFixed(1)) : null;
                avgFirstResponseTime = firstResponseTimeData.main_period.time_series_data.map(item => ({
                    label: formatDateForChart(item.date),
                    value: item.value !== null ? parseFloat(item.value.toFixed(1)) : 0
                }));
            } else {
                averageFirstResponseTimeSeconds = t('db.noDataAvailable');
                firstResponseTimePercentageChange = null;
                avgFirstResponseTime = [];
                avgFirstResponseTimeError = 'No data available from backend for Average First Response Time.';
            }

            // Process Average Response Time (seconds) Daily
            if (!averageResponseTimeResponse.ok) {
                const errorBody = await averageResponseTimeResponse.text();
                throw new Error(`HTTP error! status: ${averageResponseTimeResponse.status}, message: ${averageResponseTimeResponse.statusText || errorBody}`);
            }
            const averageResponseTimeData: ScoreAndTimeSeriesAPIResponse = await averageResponseTimeResponse.json();
            console.log("Fetched Average Response Time data:", averageResponseTimeData);
            if (averageResponseTimeData && averageResponseTimeData.main_period) {
                averageResponseTimeSeconds = averageResponseTimeData.main_period.metric_value !== null ? parseFloat(averageResponseTimeData.main_period.metric_value.toFixed(1)) : null;
                averageResponseTimePercentageChange = averageResponseTimeData.percentage_change !== null ? parseFloat(averageResponseTimeData.percentage_change.toFixed(1)) : null;
                avgResponseTime = averageResponseTimeData.main_period.time_series_data.map(item => ({
                    label: formatDateForChart(item.date),
                    value: item.value !== null ? parseFloat(item.value.toFixed(1)) : 0
                }));
            } else {
                averageResponseTimeSeconds = t('db.noDataAvailable');
                averageResponseTimePercentageChange = null;
                avgResponseTime = [];
                avgResponseTimeError = 'No data available from backend for Average Response Time.';
            }

            // Process Average Response Time (seconds): Agent vs. Chatbot
            if (!responderResponseTimeResponse.ok) {
                const errorBody = await responderResponseTimeResponse.text();
                throw new Error(`HTTP error! status: ${responderResponseTimeResponse.status}, message: ${responderResponseTimeResponse.statusText || errorBody}`);
            }
            const responderResponseTimeData: ResponderResponseTimeAPIResponse[] = await responderResponseTimeResponse.json();
            console.log("Fetched Agent vs. Chatbot data:", responderResponseTimeData);
            if (!Array.isArray(responderResponseTimeData) || responderResponseTimeData.length === 0) {
                agentChatbotComparisonData = [];
                agentChatbotError = 'No data available from backend for Agent vs. Chatbot comparison.';
            } else {
                agentChatbotComparisonData = responderResponseTimeData.map(item => ({
                    type: item.responder_type === 'agent' ? 'Agent' : 'Chatbot',
                    count: parseFloat((item.raw_avg ?? 0).toFixed(1))
                }));
            }

            // Process Total Sentiment Count
            if (!sentimentSummaryResponse.ok) {
                const errorBody = await sentimentSummaryResponse.text();
                throw new Error(`HTTP error! status: ${sentimentSummaryResponse.status}, message: ${sentimentSummaryResponse.statusText || errorBody}`);
            }
            const sentimentSummaryData: SentimentAnalysisSummaryAPIResponse[] = await sentimentSummaryResponse.json();
            console.log("Fetched Total Sentiment Count data:", sentimentSummaryData);
            let totalPositive = 0;
            let totalNeutral = 0;
            let totalNegative = 0;
            if (Array.isArray(sentimentSummaryData) && sentimentSummaryData.length > 0) {
                sentimentSummaryData.forEach(item => {
                    totalPositive += item.positive;
                    totalNeutral += item.neutral;
                    totalNegative += item.negative;
                });
            } else {
                sentimentSummaryError = 'No data available from backend for Total Sentiment Count.';
            }
            overallSentimentAmounts = [
                { label: 'Positive', value: totalPositive },
                { label: 'Neutral', value: totalNeutral },
                { label: 'Negative', value: totalNegative },
            ];
            overallSentimentColors = overallSentimentAmounts.map(item => {
                if (item.label === 'Positive') return COLORS.green;
                if (item.label === 'Neutral') return COLORS.silver;
                if (item.label === 'Negative') return COLORS.red;
                return COLORS.lightGray; // Fallback
            });

            // Process Daily Sentiment Count (Time Series)
            if (!sentimentTimeSeriesResponse.ok) {
                const errorBody = await sentimentTimeSeriesResponse.text();
                throw new Error(`HTTP error! status: ${sentimentTimeSeriesResponse.status}, message: ${sentimentTimeSeriesResponse.statusText || errorBody}`);
            }
            const sentimentTimeSeriesData: SentimentTimeSeriesAPIResponse[] = await sentimentTimeSeriesResponse.json();
            console.log("Fetched Daily Sentiment Count data:", sentimentTimeSeriesData);
            if (!Array.isArray(sentimentTimeSeriesData) || sentimentTimeSeriesData.length === 0) {
                dailySentimentTimeline = [];
                dailySentimentError = 'No data available from backend for Daily Sentiment Count.';
            } else {
                dailySentimentTimeline = sentimentTimeSeriesData.map(item => ({
                    label: formatDateForChart(item.time),
                    positive: item.positive,
                    neutral: item.neutral,
                    negative: item.negative,
                }));
            }

            // Process Sentiment Count by Case Type
            if (!sentimentByCaseTypeResponse.ok) {
                const errorBody = await sentimentByCaseTypeResponse.text();
                throw new Error(`HTTP error! status: ${sentimentByCaseTypeResponse.status}, message: ${sentimentByCaseTypeResponse.statusText || errorBody}`);
            }
            const sentimentByCaseTypeData: SentimentByCaseTypeAPIResponse[] = await sentimentByCaseTypeResponse.json();
            console.log("Fetched Sentiment by Case Type data:", sentimentByCaseTypeData);
            if (!Array.isArray(sentimentByCaseTypeData) || sentimentByCaseTypeData.length === 0) {
                productSentimentAmounts = [];
                caseTypeSentimentError = 'No data available from backend for Sentiment by Case Type.';
            } else {
                productSentimentAmounts = sentimentByCaseTypeData.map(item => ({
                    product: item.case_type,
                    positive: item.positive,
                    neutral: item.neutral,
                    negative: item.negative,
                }));
            }

        } catch (error: any) {
            console.error('Error fetching dashboard data:', error);
            // Set all error states and clear data if any fetch fails
            csatError = `Failed to load CSAT data: ${error.message}`;
            averageCSAT = t('db.noDataAvailable');
            csatPercentageChange = null;
            avgCSAT = [];

            avgFirstResponseTimeError = `Failed to load Average First Response Time data: ${error.message}`;
            averageFirstResponseTimeSeconds = t('db.noDataAvailable');
            firstResponseTimePercentageChange = null;
            avgFirstResponseTime = [];

            avgResponseTimeError = `Failed to load Average Response Time data: ${error.message}`;
            averageResponseTimeSeconds = t('db.noDataAvailable');
            averageResponseTimePercentageChange = null;
            avgResponseTime = [];

            agentChatbotError = `Failed to load Agent vs. Chatbot data: ${error.message}`;
            agentChatbotComparisonData = [];

            sentimentSummaryError = `Failed to load Total Sentiment Count data: ${error.message}`;
            overallSentimentAmounts = [];
            overallSentimentColors = [];

            dailySentimentError = `Failed to load Daily Sentiment Count data: ${error.message}`;
            dailySentimentTimeline = [];

            caseTypeSentimentError = `Failed to load Sentiment by Case Type data: ${error.message}`;
            productSentimentAmounts = [];

        } finally {
            // Ensure all loading states are set to false in the end
            isLoadingCSAT = false;
            isLoadingAvgFirstResponseTime = false;
            isLoadingAvgResponseTime = false;
            isLoadingAgentChatbot = false;
            isLoadingSentimentSummary = false;
            isLoadingDailySentiment = false;
            isLoadingCaseTypeSentiment = false;
        }
    }


    onMount(() => {
        console.log("WorkQualityTab component mounted: initiating fetchData");
        fetchData();
    });

    // Re-run fetchData whenever these reactive variables change, including isThaiLocale
    $: timeRange, startDateCustom, endDateCustom, isThaiLocale, fetchData();
</script>

{#if isAgentChatbotExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.averageResponseTimeSecondsAgentVsChatbot')}</h3>
                <button on:click={() => isAgentChatbotExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingAgentChatbot}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if agentChatbotError || agentChatbotComparisonData.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={agentChatbotComparisonData}
                        label=""
                        barColor={COLORS.blue}
                        chartType="verticalBar"
                        labelKey="type"
                        valueKey="count"
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isDailyCsatExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.averageCsatScoreOutOf5Daily')}</h3>
                <button on:click={() => isDailyCsatExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingCSAT}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if csatError || avgCSAT.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgCSAT}
                        chartLabel={t('dbWorkQuality.averageCsat')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isDailyFirstResponseTimeExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.averageFirstResponseTimeSecondsDaily')}</h3>
                <button on:click={() => isDailyFirstResponseTimeExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingAvgFirstResponseTime}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if avgFirstResponseTimeError || avgFirstResponseTime.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgFirstResponseTime}
                        chartLabel={t('dbWorkQuality.timeSeconds')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isDailyResponseTimeExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.averageResponseTimeSecondsDaily')}</h3>
                <button on:click={() => isDailyResponseTimeExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingAvgResponseTime}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if avgResponseTimeError || avgResponseTime.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgResponseTime}
                        chartLabel={t('dbWorkQuality.timeSeconds')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isOverallSentimentExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.totalSentimentCount')}</h3>
                <button on:click={() => isOverallSentimentExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingSentimentSummary}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if sentimentSummaryError || overallSentimentAmounts.length === 0 || overallSentimentAmounts.every(s => s.value === 0)}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={overallSentimentAmounts}
                        label={t('dbWorkQuality.ticketCount')}
                        barColor={overallSentimentColors}
                        borderColor={overallSentimentColors}
                        chartType="verticalBar"
                        labelKey="label"
                        valueKey="value"
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isDailySentimentExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.dailySentimentCount')}</h3>
                <button on:click={() => isDailySentimentExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingDailySentiment}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if dailySentimentError || dailySentimentTimeline.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={dailySentimentTimeline}
                        label={t('dbWorkQuality.ticketCount')}
                        chartType="stackedVerticalBar"
                        labelKey="label"
                        stackedKeys={['positive', 'neutral', 'negative']}
                        stackedColors={{
                            positive: COLORS.green,
                            neutral: COLORS.silver,
                            negative: COLORS.red
                        }}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isCaseTypeSentimentExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbWorkQuality.sentimentCountClosedTicketsByCaseType')}</h3>
                <button on:click={() => isCaseTypeSentimentExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingCaseTypeSentiment}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if caseTypeSentimentError || productSentimentAmounts.length === 0 || productSentimentAmounts.every(p => p.positive === 0 && p.neutral === 0 && p.negative === 0)}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={productSentimentAmounts} label={t('dbWorkQuality.ticketCount')}
                        chartType="stackedHorizontalBar"
                        labelKey="product"
                        stackedKeys={['positive', 'neutral', 'negative']}
                        stackedColors={{
                            positive: COLORS.green,
                            neutral: COLORS.silver,
                            negative: COLORS.red
                        }}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

<div class="flex flex-col gap-6">
    <div class="flex flex-col md:flex-row md:items-center justify-end gap-4 bg-white rounded-lg shadow-md p-4">
        <div class="flex flex-wrap items-center justify-start sm:justify-end gap-2 w-full md:w-auto">
            <label for="time-range-filter" class="text-gray-700 font-medium">{t('db.timeRange')}:</label>
            <select id="time-range-filter" bind:value={timeRange} class="border border-gray-300 rounded-md p-2 text-gray-700 w-full sm:w-auto">
                <option value="Last 7 Days">{t('db.last7Days')}</option>
                <option value="Last 30 Days">{t('db.last30Days')}</option>
                <option value="This Month">{t('db.thisMonth')}</option>
                <option value="Last Month">{t('db.lastMonth')}</option>
                <option value="Custom">{t('db.custom')}</option>
            </select>
            {#if timeRange === 'Custom'}
                <input type="date" bind:value={startDateCustom} class="border border-gray-300 rounded-md p-2 w-full sm:w-auto" />
                <input type="date" bind:value={endDateCustom} class="border border-gray-300 rounded-md p-2 w-full sm:w-auto" />
            {/if}
            <button
                on:click={clearFilters}
                class="bg-transparent hover:bg-gray-100 text-red-700 font-semibold py-2 px-4 border border-red-500 hover:border-transparent rounded-md text-sm mt-2 sm:mt-0 w-full sm:w-auto whitespace-nowrap flex-shrink-0" >
                {t('db.clearFilters')}
            </button>
            <!-- <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm mt-2 sm:mt-0 w-full sm:w-auto whitespace-nowrap flex-shrink-0" >
                {t('db.downloadCsvFullPage')}
            </button> -->
        </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div class="grid grid-cols-1 gap-6">
            <div class="bg-white rounded-lg shadow-md p-6 flex flex-col justify-between">
                <ScoreCard
                    title={t('dbWorkQuality.averageCsatScoreOutOf5')}
                    value={averageCSAT}
                    valueColor="text-black-600"
                    trendValue={csatPercentageChange}
                    trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-6 flex flex-col justify-between">
                <ScoreCard
                    title={t('dbWorkQuality.averageFirstResponseTimeSeconds')}
                    value={averageFirstResponseTimeSeconds}
                    valueColor="text-black-600"
                    trendValue={firstResponseTimePercentageChange}
                    trendUnit="%"
                    isTrendPositiveIsGood={false}
                />
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.averageResponseTimeSecondsAgentVsChatbot')}</h2>
                <div class="flex gap-2">
                    <button
                        on:click={() => isAgentChatbotExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.expand')}
                    </button>
                    <button
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-[14rem] flex items-center justify-center">
                {#if isLoadingAgentChatbot}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if agentChatbotError || agentChatbotComparisonData.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={agentChatbotComparisonData}
                        label=""
                        barColor={COLORS.blue}
                        chartType="verticalBar"
                        labelKey="type"
                        valueKey="count"
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>

    </div>
    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.averageCsatScoreOutOf5Daily')}</h2>
                <div class="flex gap-2">
                    <button
                        on:click={() => isDailyCsatExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.expand')}
                    </button>
                    <button
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-64 flex items-center justify-center">
                {#if isLoadingCSAT}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if csatError || avgCSAT.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgCSAT}
                        chartLabel={t('dbWorkQuality.averageCsat')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                    />
                {/if}
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.averageFirstResponseTimeSecondsDaily')}</h2>
                <div class="flex gap-2">
                    <button
                        on:click={() => isDailyFirstResponseTimeExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.expand')}
                    </button>
                    <button
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-64 flex items-center justify-center">
                {#if isLoadingAvgFirstResponseTime}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if avgFirstResponseTimeError || avgFirstResponseTime.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgFirstResponseTime}
                        chartLabel={t('dbWorkQuality.timeSeconds')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                    />
                {/if}
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.averageResponseTimeSecondsDaily')}</h2>
                <div class="flex gap-2">
                    <button
                        on:click={() => isDailyResponseTimeExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.expand')}
                    </button>
                    <button
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-64 flex items-center justify-center">
                {#if isLoadingAvgResponseTime}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if avgResponseTimeError || avgResponseTime.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <LineChart
                        data={avgResponseTime}
                        chartLabel={t('dbWorkQuality.timeSeconds')}
                        lineColor={COLORS.blue}
                        showDataLabels={false}
                        showLegend={false}
                    />
                {/if}
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.totalSentimentCount')}</h2>
                <div class="flex gap-2">
                    <button
                        on:click={() => isOverallSentimentExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.expand')}
                    </button>
                    <button
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-64 flex items-center justify-center">
                {#if isLoadingSentimentSummary}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if sentimentSummaryError || overallSentimentAmounts.length === 0 || overallSentimentAmounts.every(s => s.value === 0)}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={overallSentimentAmounts}
                        label={t('dbWorkQuality.ticketCount')}
                        barColor={overallSentimentColors}
                        borderColor={overallSentimentColors}
                        chartType="verticalBar"
                        labelKey="label"
                        valueKey="value"
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.dailySentimentCount')}</h2>
                <div class="flex gap-2">
                    <button
                        on:click={() => isDailySentimentExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.expand')}
                    </button>
                    <button
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-64 flex items-center justify-center">
                {#if isLoadingDailySentiment}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if dailySentimentError || dailySentimentTimeline.length === 0}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={dailySentimentTimeline}
                        label={t('dbWorkQuality.ticketCount')}
                        chartType="stackedVerticalBar"
                        labelKey="label"
                        stackedKeys={['positive', 'neutral', 'negative']}
                        stackedColors={{
                            positive: COLORS.green,
                            neutral: COLORS.silver,
                            negative: COLORS.red
                        }}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>

        <div class="bg-white rounded-lg shadow-md p-6">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbWorkQuality.sentimentCountClosedTicketsByCaseType')}</h2>
                <div class="flex gap-2">
                    <button
                        on:click={() => isCaseTypeSentimentExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.expand')}
                    </button>
                    <button
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-[15rem] flex items-center justify-center">
                {#if isLoadingCaseTypeSentiment}
                    <div class="flex flex-col items-center justify-center text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if caseTypeSentimentError || productSentimentAmounts.length === 0 || productSentimentAmounts.every(p => p.positive === 0 && p.neutral === 0 && p.negative === 0)}
                    <div class="text-gray-600 text-center text-lg">
                        <p>{t('db.noDataAvailable')}</p>
                    </div>
                {:else}
                    <BarChart
                        data={productSentimentAmounts} label={t('dbWorkQuality.ticketCount')}
                        chartType="stackedHorizontalBar"
                        labelKey="product"
                        stackedKeys={['positive', 'neutral', 'negative']}
                        stackedColors={{
                            positive: COLORS.green,
                            neutral: COLORS.silver,
                            negative: COLORS.red
                        }}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
</div>