<script lang="ts">
    import BarChart from '$lib/components/dashboard/BarChart.svelte';
    import LineChart from '$lib/components/dashboard/LineChart.svelte';
    import ScoreCard from '$lib/components/dashboard/ScoreCard.svelte';
    import { COLORS } from '$lib/components/dashboard/colors';
    import { t, dict } from '$lib/stores/i18n'; // Keep t and dict from i18n
    import { currentLanguage } from '$lib/stores/languagePreference'; // Corrected import path for currentLanguage
    import { onMount } from 'svelte';

    // Define API Base URL
    const API_BASE_URL: string = import.meta.env.VITE_PUBLIC_API_BASE_URL || 'https://backend.salmate-staging.aibrainlab.co/dashboard/api/';
    // console.log("API Base URL detected:", API_BASE_URL);

    // Local state for filters
    let timeRange: string = 'Last 7 Days';
    let startDateCustom: string = '';
    let endDateCustom: string = '';

    // Data fetched from backend - Scorecard values
    // Allow null for "no data" for scorecards
    let allTickets: number | null = null;
    let allTicketsTrend: number | null = null;
    let allClosedTickets: number | null = null;
    let allClosedTicketsTrend: number | null = null;
    let chatVolume: number | null = null;
    let chatVolumeTrend: number | null = null;
    let closedRate: number | null = null;
    let closedRateTrend: number | null = null;
    let avgHandlingTime: number | null = null;
    let handlingTimeRate: number | null = null;
    let avgResponseTime: number | null = null;
    let responseTimeTrend: number | null = null;
    let responseRateWithin6s: number | null = null;
    let responseRateWithin6sTrend: number | null = null;
    let handlingRateWithin5mins: number | null = null;
    let handlingRateWithin5minsTrend: number | null = null;

    // Data fetched from backend - Charts and tables
    let ticketStatusData: TicketStatusDataItem[] = [];
    let unclosedTickets: UnclosedTicket[] = []; // Not used in provided snippet, but keeping for completeness
    let closedTickets: ClosedTicket[] = []; // Not used in provided snippet, but keeping for completeness
    let closedTicketsByCaseType: CaseTypeDataItem[] = [];
    let closedTicketsBySubCaseType: SubCaseTypeDataItem[] = [];
    let closedCaseSubCaseTable: CaseSubCaseInfo[] = [];
    let overdueUnclosedTickets: UnclosedTicket[] = [];
    let overdueClosedTickets: ClosedTicket[] = []; // Data for overdue closed tickets

    // Loading and error states
    // Adjusted error states to be specific to charts/tables for frontend display.
    let isLoadingTicketStatusChart: boolean = true;
    let isLoadingUnclosedTickets: boolean = true; // Not used in provided snippet
    let isLoadingClosedTickets: boolean = true; // Not used in provided snippet
    let isLoadingCaseTypeChart: boolean = true;
    let isLoadingSubCaseTypeChart: boolean = true;
    let isLoadingCaseSubCaseTable: boolean = true;
    let isLoadingOverdueUnclosedTickets: boolean = true;
    let isLoadingOverdueClosedTickets: boolean = true;

    // Specific error messages for charts/tables, set to generic for frontend
    let ticketStatusChartError: string | null = null;
    let unclosedTicketsError: string | null = null; // Not used in provided snippet
    let closedTicketsError: string | null = null; // Not used in provided snippet
    let caseTypeChartError: string | null = null;
    let subCaseTypeChartError: string | null = null;
    let caseSubCaseTableError: string | null = null;
    let overdueUnclosedTicketsError: string | null = null;
    let overdueClosedTicketsError: string | null = null; // Error state for overdue closed tickets

    // Expand state variables for modals
    let isTicketStatusChartExpanded: boolean = false;
    let isUnclosedTicketsExpanded: boolean = false;
    let isClosedTicketsExpanded: boolean = false;
    let isCaseTypeChartExpanded: boolean = false;
    let isSubCaseTypeChartExpanded: boolean = false;
    let isCaseSubCaseTableExpanded: boolean = false;
    let isOverdueUnclosedTicketsExpanded: boolean = false;
    let isOverdueClosedTicketsExpanded: boolean = false; // Expand state for overdue closed tickets


    // Define API response interfaces (no changes here from your provided code)
    interface MetricAPIResponse {
        main_period: {
            start_date: string;
            end_date: string;
            metric_value: number | null; // Explicitly allow null
            time_series_data: { date: string; value: number | null }[];
        };
        comparison_period: {
            start_date: string;
            end_date: string;
            metric_value: number | null; // Explicitly allow null
            time_series_data: { date: string; value: number | null }[];
        };
        percentage_change: number | null; // Explicitly allow null
        units: string;
    }

    interface TicketStatusAPIResponse {
        status: string;
        ticket_count: number;
    }

    interface UnclosedTicketsAPIResponse {
        ticket_number: string;
        status: string;
        customer: string;
        priority: 'High' | 'Medium' | 'Low';
        sentiment: 'Positive' | 'Neutral' | 'Negative';
        agent: string;
        created_time: string;
        closed_time: string | null; // Can be null for unclosed tickets
        overdue_time: string; // "D HH:MM:SS.ms" format
    }

    // UPDATED: ClosedTicketsAPIResponse to include overdue_time, same structure as unclosed but with closed_time always present
    interface ClosedTicketsAPIResponse {
        ticket_number: string;
        status: string;
        customer: string;
        priority: 'High' | 'Medium' | 'Low';
        sentiment: 'Positive' | 'Neutral' | 'Negative';
        agent: string;
        created_time: string;
        closed_time: string; // Will always be a string for closed tickets
        overdue_time: string; // "D HH:MM:SS.ms" format
    }

    interface ClosedTicketsByCaseTypeAPIResponse {
        case_type: string;
        ticket_count: number;
    }

    interface ClosedTicketsBySubCaseTypeAPIResponse {
        case_topic: string;
        ticket_count: number;
    }

    interface ClosedTicketsByCaseTypeAndTopicAPIResponse {
        case_type: string;
        case_topic: string;
        ticket_count: number;
    }

    // --- Interfaces for other visualizations ---
    interface TicketStatusDataItem {
        status: string;
        amount: number;
    }

    interface UnclosedTicket {
        ticketNo: string;
        ticketStatus: string;
        customerName: string;
        priority: 'High' | 'Medium' | 'Low';
        sentiment: 'Positive' | 'Neutral' | 'Negative';
        agentName: string;
        createdDateTime: string;
        currentDateTime: string;
        totalUsedTime: number; // Changed to number (total seconds)
    }

    // UPDATED: ClosedTicket interface to store totalUsedTime as a number (total seconds)
    interface ClosedTicket {
        ticketNo: string;
        ticketStatus: string;
        customerName: string;
        priority: 'High' | 'Medium' | 'Low';
        sentiment: 'Positive' | 'Neutral' | 'Negative';
        agentName: string;
        createdDateTime: string;
        closedDateTime: string; // Renamed from closed_time to match convention, will be a string
        totalUsedTime: number; // Changed to number (total seconds)
        id: string;
    }

    interface CaseTypeDataItem {
        caseType: string;
        count: number;
    }

    interface SubCaseTypeDataItem {
        subCaseType: string;
        count: number;
    }

    interface CaseSubCaseInfo {
        caseType: string;
        subCaseType: string;
        count: number;
    }

    // SORTING STATE AND FUNCTIONALITY
    let currentSort: { [key: string]: { column: string; direction: 'asc' | 'desc' | null } } = {
        unclosedTickets: { column: 'totalUsedTime', direction: 'desc' },
        closedTickets: { column: 'totalUsedTime', direction: 'desc' },
        closedCaseSubCaseTable: { column: 'count', direction: 'desc' },
        overdueUnclosedTickets: { column: 'totalUsedTime', direction: 'desc' },
        overdueClosedTickets: { column: 'totalUsedTime', direction: 'desc' }
    };

    function sortTable<T>(dataArray: T[], key: keyof T, tableName: string): T[] {
        const currentTableSort = currentSort[tableName] || { column: null, direction: null };
        let newDirection: 'asc' | 'desc';

        if (currentTableSort.column === key) {
            newDirection = currentTableSort.direction === 'asc' ? 'desc' : 'asc';
        } else {
            if (key === 'totalUsedTime' || key === 'count') {
                newDirection = 'desc';
            } else {
                newDirection = 'asc';
            }
        }

        currentSort = {
            ...currentSort,
            [tableName]: { column: String(key), direction: newDirection }
        };

        return [...dataArray].sort((a, b) => {
            const aValue = a[key];
            const bValue = b[key];

            if (key === 'priority') {
                const priorityOrder = { 'High': 3, 'Medium': 2, 'Low': 1 };
                const aP = priorityOrder[aValue as 'High' | 'Medium' | 'Low'];
                const bP = priorityOrder[bValue as 'High' | 'Medium' | 'Low'];
                return newDirection === 'asc' ? aP - bP : bP - aP;
            }
            if (key === 'sentiment') {
                const sentimentOrder = { 'Positive': 3, 'Neutral': 2, 'Negative': 1 };
                const aS = sentimentOrder[aValue as 'Positive' | 'Neutral' | 'Negative'];
                const bS = sentimentOrder[bValue as 'Positive' | 'Neutral' | 'Negative'];
                return newDirection === 'asc' ? aS - bS : bS - aS;
            }

            // Handle date strings (createdDateTime, currentDateTime, closedDateTime)
            if (['createdDateTime', 'currentDateTime', 'closedDateTime'].includes(String(key))) {
                const valA = new Date(aValue as string).getTime();
                const valB = new Date(bValue as string).getTime();
                return newDirection === 'asc' ? valA - valB : valB - valA;
            }
            // Handle numeric values (totalUsedTime, count)
            else if (typeof aValue === 'number' && typeof bValue === 'number') {
                return newDirection === 'asc' ? aValue - bValue : bValue - aValue;
            }
            // Handle other string comparisons
            else if (typeof aValue === 'string' && typeof bValue === 'string') {
                return newDirection === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
            }
            return 0;
        });
    }

    function applyInitialSort<T>(dataArray: T[], tableName: string): T[] {
        const sortState = currentSort[tableName];
        if (sortState && sortState.column) {
            const tempSort = {
                ...currentSort,
                [tableName]: { column: sortState.column, direction: sortState.direction }
            };
            const sortedArray = [...dataArray].sort((a, b) => {
                const key = tempSort[tableName].column as keyof T;
                const aValue = a[key];
                const bValue = b[key];
                const direction = tempSort[tableName].direction;

                if (key === 'priority') {
                    const priorityOrder = { 'High': 3, 'Medium': 2, 'Low': 1 };
                    const aP = priorityOrder[aValue as 'High' | 'Medium' | 'Low'];
                    const bP = priorityOrder[bValue as 'High' | 'Medium' | 'Low'];
                    return direction === 'asc' ? aP - bP : bP - aP;
                }
                if (key === 'sentiment') {
                    const sentimentOrder = { 'Positive': 3, 'Neutral': 2, 'Negative': 1 };
                    const aS = sentimentOrder[aValue as 'Positive' | 'Neutral' | 'Negative'];
                    const bS = sentimentOrder[bValue as 'Positive' | 'Neutral' | 'Negative'];
                    return direction === 'asc' ? aS - bS : bS - aS;
                }

                // Handle date strings (createdDateTime, currentDateTime, closedDateTime)
                if (['createdDateTime', 'currentDateTime', 'closedDateTime'].includes(String(key))) {
                    const valA = new Date(aValue as string).getTime();
                    const valB = new Date(bValue as string).getTime();
                    return direction === 'asc' ? valA - valB : valB - valA;
                }
                // Handle numeric values (totalUsedTime, count)
                else if (typeof aValue === 'number' && typeof bValue === 'number') {
                    return direction === 'asc' ? aValue - bValue : bValue - aValue;
                }
                // Handle other string comparisons
                else if (typeof aValue === 'string' && typeof bValue === 'string') {
                    return direction === 'asc' ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
                }
                return 0;
            });
            return sortedArray;
        }
        return dataArray;
    }

    function formatDate(date: Date): string {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }

    // Helper to format date-time strings for display (e.g., "DD Mon YYYY, H:MM") - CHANGED TO 24-HOUR
    function formatDateTimeForDisplay(isoString: string | null): string {
        if (!isoString) return 'N/A';
        try {
            const date = new Date(isoString);
            const lang = $currentLanguage; // Access the store value
            
            return date.toLocaleDateString(lang, {
                day: '2-digit',
                month: 'short',
                year: 'numeric',
                hour: '2-digit',      // Changed to '2-digit' for 24-hour compatibility
                minute: '2-digit',    // Keep '2-digit'
                hour12: false         // CHANGED: Set to false for 24-hour format
            });
        } catch (e) {
            console.error("Error formatting date-time:", e);
            return isoString; // Return original if parsing fails
        }
    }


    function getDateRange(range: string, customStartDate: string, customEndDate: string): { startDate: string | undefined; endDate: string | undefined } {
        let startDate: string | undefined;
        let endDate: string | undefined;
        const today = new Date();

        if (range === 'Last 7 Days') {
            const sevenDaysAgo = new Date(today);
            sevenDaysAgo.setDate(today.getDate() - 7);
            startDate = formatDate(sevenDaysAgo);
            endDate = formatDate(today);
        } else if (range === 'Last 30 Days') {
            const thirtyDaysAgo = new Date(today);
            thirtyDaysAgo.setDate(today.getDate() - 30);
            startDate = formatDate(thirtyDaysAgo);
            endDate = formatDate(today);
        } else if (range === 'This Month') {
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            startDate = formatDate(firstDayOfMonth);
            endDate = formatDate(today);
        } else if (range === 'Last Month') {
            const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() -1 , 1);
            const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
            startDate = formatDate(firstDayOfLastMonth);
            endDate = formatDate(lastDayOfLastMonth);
        } else if (range === 'Custom') {
            startDate = customStartDate;
            endDate = customEndDate;
        }
        return { startDate, endDate };
    }

    // Helper function to parse API's overdue_time string ("D HH:MM:SS.ms") into total seconds
    function parseApiOverdueTime(apiTimeStr: string | null): number {
        if (!apiTimeStr) return 0;

        const parts = apiTimeStr.split(' '); // "21 18:38:06.427410" -> ["21", "18:38:06.427410"]
        if (parts.length !== 2) {
            console.warn(`Unexpected overdue_time format: ${apiTimeStr}`);
            return 0; // If null or empty string, returns 0. OK.
        }

        const days = parseInt(parts[0], 10); // "21" -> 21. OK.
        const timeParts = parts[1].split(':'); // "18:38:06.427410" -> ["18", "38", "06.427410"]

        if (timeParts.length !== 3) {
            console.warn(`Unexpected time part format in overdue_time: ${apiTimeStr}`);
            return 0; // Incorrect time part format returns 0. OK.
        }

        const hours = parseInt(timeParts[0], 10); // "18" -> 18. OK.
        const minutes = parseInt(timeParts[1], 10); // "38" -> 38. OK.
        const seconds = parseFloat(timeParts[2]); // "06.427410" -> 6.42741. OK.

        return days * 24 * 3600 + hours * 3600 + minutes * 60 + seconds;
    }

    /**
     * Helper function to format seconds into localized "Xd Yh Zm" string for display.
     * This is the existing function and works well with the new `totalUsedTime` (number).
     */
    function formatOverdueTime(seconds: number): string {
        const days = Math.floor(seconds / (3600 * 24));
        const hours = Math.floor((seconds % (3600 * 24)) / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);

        let parts = [];
        if (days > 0) parts.push(`${days}${t('db.timeUnitDay')}`);
        if (hours > 0) parts.push(`${hours}${t('db.timeUnitHour')}`);
        if (minutes > 0) parts.push(`${minutes}${t('db.timeUnitMinute')}`);

        if (parts.length === 0) return `0${t('db.timeUnitMinute')}`; // Default to 0 minutes if less than a minute
        return parts.join(' ');
    }

    /**
     * Resets the filter state variables to their default values.
     */
    function clearFilters() {
        timeRange = 'Last 7 Days';
        startDateCustom = '';
        endDateCustom = '';
    }

    // Helper to check for zero (or null from API) and convert main value to null if needed
    const setValueOrNull = (value: number | null): number | null => {
        if (value === 0) {
            return null;
        }
        return value;
    };

    // New helper function to set trend to null if the main value is (or becomes) null
    const setTrendOrNull = (mainValue: number | null, trendValueFromAPI: number | null): number | null => {
        if (mainValue === null) { // If the main metric is null (meaning 'No Data Available')
            return null; // Then the trend should also be null
        }
        return trendValueFromAPI; // Otherwise, use the trend value from the API
    };

    async function fetchData() {
        console.log("fetchData called for Chat Performance dashboards/tables");

        // Reset loading states for charts/tables
        isLoadingTicketStatusChart = true;
        isLoadingUnclosedTickets = true;
        isLoadingClosedTickets = true;
        isLoadingCaseTypeChart = true;
        isLoadingSubCaseTypeChart = true;
        isLoadingCaseSubCaseTable = true;
        isLoadingOverdueUnclosedTickets = true;
        isLoadingOverdueClosedTickets = true;

        // Reset error states
        ticketStatusChartError = null;
        unclosedTicketsError = null;
        closedTicketsError = null;
        caseTypeChartError = null;
        subCaseTypeChartError = null;
        caseSubCaseTableError = null;
        overdueUnclosedTicketsError = null;
        overdueClosedTicketsError = null;

        // Reset scorecard values to null before fetching to indicate loading/no data
        chatVolume = null;
        chatVolumeTrend = null;
        allTickets = null;
        allTicketsTrend = null;
        allClosedTickets = null;
        allClosedTicketsTrend = null;
        closedRate = null;
        closedRateTrend = null;
        avgResponseTime = null;
        responseTimeTrend = null;
        responseRateWithin6s = null;
        responseRateWithin6sTrend = null;
        avgHandlingTime = null;
        handlingTimeRate = null;
        handlingRateWithin5mins = null;
        handlingRateWithin5minsTrend = null;


        const { startDate, endDate } = getDateRange(timeRange, startDateCustom, endDateCustom);
        const urlParams = new URLSearchParams();
        if (startDate) urlParams.append('start_date', startDate);
        if (endDate) urlParams.append('end_date', endDate);

        // Array to hold all fetch promises
        const fetchPromises = [];

        // --- Fetch Total Incoming Messages ScoreCard ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}incoming-message-count/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: MetricAPIResponse) => {
                    chatVolume = setValueOrNull(data.main_period.metric_value);
                    chatVolumeTrend = setTrendOrNull(chatVolume, data.percentage_change);
                })
                .catch(error => {
                    console.error('Error fetching total incoming messages data:', error);
                    chatVolume = null;
                    chatVolumeTrend = null;
                })
        );

        // --- Fetch Total Agent Tickets ScoreCard ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}distinct-incoming-tickets-count/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: MetricAPIResponse) => {
                    allTickets = setValueOrNull(data.main_period.metric_value);
                    allTicketsTrend = setTrendOrNull(allTickets, data.percentage_change);
                })
                .catch(error => {
                    console.error('Error fetching total agent tickets data:', error);
                    allTickets = null;
                    allTicketsTrend = null;
                })
        );

        // --- Fetch Total Agent Closed Tickets ScoreCard ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}closed-ticket-count/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: MetricAPIResponse) => {
                    allClosedTickets = setValueOrNull(data.main_period.metric_value);
                    allClosedTicketsTrend = setTrendOrNull(allClosedTickets, data.percentage_change);
                })
                .catch(error => {
                    console.error('Error fetching total agent closed tickets data:', error);
                    allClosedTickets = null;
                    allClosedTicketsTrend = null;
                })
        );

        // --- Fetch Agent Ticket Closure Rate vs Incoming (%) ScoreCard ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}closed-ticket-rate/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: MetricAPIResponse) => {
                    closedRate = data.main_period.metric_value;
                    closedRateTrend = setTrendOrNull(closedRate, data.percentage_change);
                })
                .catch(error => {
                    console.error('Error fetching closed ticket rate data:', error);
                    closedRate = null;
                    closedRateTrend = null;
                })
        );

        // --- Fetch Average Agent Response Time (seconds) ScoreCard ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}average-response-time/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: MetricAPIResponse) => {
                    avgResponseTime = data.main_period.metric_value;
                    responseTimeTrend = setTrendOrNull(avgResponseTime, data.percentage_change);
                })
                .catch(error => {
                    console.error('Error fetching average response time data:', error);
                    avgResponseTime = null;
                    responseTimeTrend = null;
                })
        );

        // --- Fetch Agent Response Rate Within 6 Seconds (%) ScoreCard ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}6second-response-rate/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: MetricAPIResponse) => {
                    responseRateWithin6s = setValueOrNull(data.main_period.metric_value);
                    responseRateWithin6sTrend = setTrendOrNull(responseRateWithin6s, data.percentage_change);
                })
                .catch(error => {
                    console.error('Error fetching 6-second response rate data:', error);
                    responseRateWithin6s = null;
                    responseRateWithin6sTrend = null;
                })
        );

        // --- Fetch Average Agent Handling Time (minutes) ScoreCard ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}average-handling-time/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: MetricAPIResponse) => {
                    avgHandlingTime = data.main_period.metric_value;
                    handlingTimeRate = setTrendOrNull(avgHandlingTime, data.percentage_change);
                })
                .catch(error => {
                    console.error('Error fetching average handling time data:', error);
                    avgHandlingTime = null;
                    handlingTimeRate = null;
                })
        );

        // --- Fetch Agent Handling Rate Within 5 Minutes (%) ScoreCard ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}handling-rate-within-5min/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: MetricAPIResponse) => {
                    handlingRateWithin5mins = data.main_period.metric_value;
                    handlingRateWithin5minsTrend = setTrendOrNull(handlingRateWithin5mins, data.percentage_change);
                })
                .catch(error => {
                    console.error('Error fetching 5-minute handling rate data:', error);
                    handlingRateWithin5mins = null;
                    handlingRateWithin5minsTrend = null;
                })
        );

        // --- Fetch Agent Tickets By Status Chart Data ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}ticket-status-count/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: TicketStatusAPIResponse[]) => {
                    if (!Array.isArray(data) || data.length === 0) {
                        ticketStatusData = [];
                        ticketStatusChartError = t('db.noDataAvailable');
                    } else {
                        ticketStatusData = data.map(item => ({
                            status: item.status,
                            amount: item.ticket_count
                        }));
                    }
                })
                .catch(error => {
                    console.error('Error fetching ticket status count data:', error);
                    ticketStatusData = [];
                    ticketStatusChartError = t('db.noDataAvailable');
                })
                .finally(() => {
                    isLoadingTicketStatusChart = false;
                })
        );

        // --- Fetch Agent Closed Tickets By Case Type Chart Data ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}closed-tickets-by-case-type/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: ClosedTicketsByCaseTypeAPIResponse[]) => {
                    if (!Array.isArray(data) || data.length === 0) {
                        closedTicketsByCaseType = [];
                        caseTypeChartError = t('db.noDataAvailable');
                    } else {
                        closedTicketsByCaseType = data.map(item => ({
                            caseType: item.case_type,
                            count: item.ticket_count
                        }));
                    }
                })
                .catch(error => {
                    console.error('Error fetching closed tickets by case type data:', error);
                    closedTicketsByCaseType = [];
                    caseTypeChartError = t('db.noDataAvailable');
                })
                .finally(() => {
                    isLoadingCaseTypeChart = false;
                })
        );

        // --- Fetch Agent Closed Tickets by Sub-Case Type Chart Data ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}closed-tickets-by-case-topic/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: ClosedTicketsBySubCaseTypeAPIResponse[]) => {
                    if (!Array.isArray(data) || data.length === 0) {
                        closedTicketsBySubCaseType = [];
                        subCaseTypeChartError = t('db.noDataAvailable');
                    } else {
                        closedTicketsBySubCaseType = data.map(item => ({
                            subCaseType: item.case_topic,
                            count: item.ticket_count
                        }));
                    }
                })
                .catch(error => {
                    console.error('Error fetching closed tickets by sub-case type data:', error);
                    closedTicketsBySubCaseType = [];
                    subCaseTypeChartError = t('db.noDataAvailable');
                })
                .finally(() => {
                    isLoadingSubCaseTypeChart = false;
                })
        );

        // --- Fetch Agent Closed Tickets by Case & Sub-Case Type Table Data ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}closed-tickets-by-case-type-and-topic/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: ClosedTicketsByCaseTypeAndTopicAPIResponse[]) => {
                    if (!Array.isArray(data) || data.length === 0) {
                        closedCaseSubCaseTable = [];
                        caseSubCaseTableError = t('db.noDataAvailable');
                    } else {
                        closedCaseSubCaseTable = applyInitialSort(data.map(item => ({
                            caseType: item.case_type,
                            subCaseType: item.case_topic,
                            count: item.ticket_count
                        })), 'closedCaseSubCaseTable');
                    }
                })
                .catch(error => {
                    console.error('Error fetching closed tickets by case type and topic data:', error);
                    closedCaseSubCaseTable = [];
                    caseSubCaseTableError = t('db.noDataAvailable');
                })
                .finally(() => {
                    isLoadingCaseSubCaseTable = false;
                })
        );

        // --- Fetch Overdue Unclosed Tickets Table Data ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}overdue-unclosed-tickets/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: UnclosedTicketsAPIResponse[]) => {
                    if (!Array.isArray(data) || data.length === 0) {
                        overdueUnclosedTickets = [];
                        overdueUnclosedTicketsError = t('db.noDataAvailable');
                    } else {
                        overdueUnclosedTickets = applyInitialSort(data.map(item => ({
                            ticketNo: item.ticket_number,
                            ticketStatus: item.status,
                            customerName: item.customer,
                            priority: item.priority,
                            sentiment: item.sentiment,
                            agentName: item.agent,
                            createdDateTime: item.created_time,
                            currentDateTime: new Date().toISOString(),
                            totalUsedTime: parseApiOverdueTime(item.overdue_time)
                        })), 'overdueUnclosedTickets');
                    }
                })
                .catch(error => {
                    console.error('Error fetching overdue unclosed tickets data:', error);
                    overdueUnclosedTickets = [];
                    overdueUnclosedTicketsError = t('db.noDataAvailable');
                })
                .finally(() => {
                    isLoadingOverdueUnclosedTickets = false;
                })
        );

        // --- New: Fetch Overdue Closed Tickets Table Data ---
        fetchPromises.push(
            fetch(`${API_BASE_URL}overdue-closed-tickets/?${urlParams.toString()}`)
                .then(async response => {
                    if (!response.ok) {
                        const errorBody = await response.text();
                        throw new Error(`HTTP error! status: ${response.status}, message: ${response.statusText || errorBody}`);
                    }
                    return response.json();
                })
                .then((data: ClosedTicketsAPIResponse[]) => {
                    if (!Array.isArray(data) || data.length === 0) {
                        overdueClosedTickets = [];
                        overdueClosedTicketsError = t('db.noDataAvailable');
                    } else {
                        overdueClosedTickets = applyInitialSort(data.map(item => ({
                            ticketNo: item.ticket_number,
                            ticketStatus: item.status,
                            customerName: item.customer,
                            priority: item.priority,
                            sentiment: item.sentiment,
                            agentName: item.agent,
                            createdDateTime: item.created_time,
                            closedDateTime: item.closed_time,
                            totalUsedTime: parseApiOverdueTime(item.overdue_time),
                            // Generate a truly unique ID for Svelte's keying
                            id: `${item.ticket_number}-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`
                        })), 'overdueClosedTickets');
                    }
                })
                .catch(error => {
                    console.error('Error fetching overdue closed tickets data:', error);
                    overdueClosedTickets = [];
                    overdueClosedTicketsError = t('db.noDataAvailable');
                })
                .finally(() => {
                    isLoadingOverdueClosedTickets = false;
                })
        );

        // Execute all promises in parallel
        await Promise.all(fetchPromises);
    }

    onMount(() => {
        console.log("Component mounted: initiating fetchData");
        fetchData();
    });

    // Re-run fetchData whenever these reactive variables change
    $: timeRange, startDateCustom, endDateCustom, $currentLanguage, fetchData(); // Added $currentLanguage to trigger re-fetch

    // Helper for priority/sentiment badge colors
    function getPriorityColor(priority: 'High' | 'Medium' | 'Low'): string {
        switch (priority) {
            case 'High': return 'bg-red-100 text-red-800';
            case 'Medium': return 'bg-yellow-100 text-yellow-800';
            case 'Low': return 'bg-green-100 text-green-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }

    function getSentimentColor(sentiment: 'Positive' | 'Neutral' | 'Negative'): string {
        switch (sentiment) {
            case 'Positive': return 'bg-green-100 text-green-800';
            case 'Neutral': return 'bg-gray-100 text-gray-800';
            case 'Negative': return 'bg-red-100 text-red-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    }
</script>

{#if isTicketStatusChartExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.agentTicketsByStatus')}</h3>
                <button on:click={() => isTicketStatusChartExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingTicketStatusChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if ticketStatusChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if ticketStatusData.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={ticketStatusData}
                        chartType="horizontalBar"
                        labelKey="status"
                        valueKey="amount"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isCaseTypeChartExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.agentClosedTicketsByCaseType')}</h3>
                <button on:click={() => isCaseTypeChartExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingCaseTypeChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if caseTypeChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedTicketsByCaseType.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={closedTicketsByCaseType} chartType="horizontalBar"
                        labelKey="caseType" valueKey="count" label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isSubCaseTypeChartExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.agentClosedTicketsBySubCaseType')}</h3>
                <button on:click={() => isSubCaseTypeChartExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full h-full flex items-center justify-center">
                {#if isLoadingSubCaseTypeChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if subCaseTypeChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedTicketsBySubCaseType.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={closedTicketsBySubCaseType}
                        chartType="horizontalBar"
                        labelKey="subCaseType"
                        valueKey="count"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isCaseSubCaseTableExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.agentClosedTicketsCaseAndSubCase')}</h3>
                <button on:click={() => isCaseSubCaseTableExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full overflow-y-auto">
                {#if isLoadingCaseSubCaseTable}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if caseSubCaseTableError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedCaseSubCaseTable.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'caseType', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.caseType')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'caseType'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'subCaseType', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.subCaseType')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'subCaseType'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'count', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.count')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'count'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each closedCaseSubCaseTable as item (item.caseType + item.subCaseType)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.caseType}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.subCaseType}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.count}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isOverdueUnclosedTicketsExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.unclosedTicketsOver1Day')}</h3>
                <button on:click={() => isOverdueUnclosedTicketsExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full overflow-y-auto">
                {#if isLoadingOverdueUnclosedTickets}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if overdueUnclosedTicketsError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if overdueUnclosedTickets.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'ticketNo', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.ticketNo')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'ticketNo'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'ticketStatus', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.status')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'ticketStatus'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'customerName', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.customer')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'customerName'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'priority', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.priority')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'priority'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'sentiment', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.sentiment')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'sentiment'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'agentName', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.agent')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'agentName'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'createdDateTime', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.createdTime')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'createdDateTime'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'currentDateTime', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.currentTime')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'currentDateTime'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'totalUsedTime', 'overdueUnclosedTickets')}>
                                    {t('dbChatPerformance.totalUsedTime')}
                                    {#if currentSort.overdueUnclosedTickets.column === 'totalUsedTime'}
                                        {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each overdueUnclosedTickets as item (item.ticketNo)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.ticketStatus}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getPriorityColor(item.priority)}">
                                            {item.priority}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getSentimentColor(item.sentiment)}">
                                            {item.sentiment}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.currentDateTime)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

{#if isOverdueClosedTicketsExpanded}
    <div class="fixed inset-0 z-50 flex items-center justify-center p-4 bg-gray-800 bg-opacity-75">
        <div class="bg-white rounded-lg shadow-xl w-full h-full flex flex-col p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-2xl font-bold text-gray-800">{t('dbChatPerformance.closedTicketsOver1Day')}</h3>
                <button on:click={() => isOverdueClosedTicketsExpanded = false} class="text-gray-500 hover:text-gray-700">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="flex-grow w-full overflow-y-auto">
                {#if isLoadingOverdueClosedTickets}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-full">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if overdueClosedTicketsError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if overdueClosedTickets.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'ticketNo', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.ticketNo')}
                                    {#if currentSort.overdueClosedTickets.column === 'ticketNo'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'ticketStatus', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.status')}
                                    {#if currentSort.overdueClosedTickets.column === 'ticketStatus'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'customerName', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.customer')}
                                    {#if currentSort.overdueClosedTickets.column === 'customerName'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'priority', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.priority')}
                                    {#if currentSort.overdueClosedTickets.column === 'priority'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'sentiment', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.sentiment')}
                                    {#if currentSort.overdueClosedTickets.column === 'sentiment'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'agentName', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.agent')}
                                    {#if currentSort.overdueClosedTickets.column === 'agentName'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'createdDateTime', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.createdTime')}
                                    {#if currentSort.overdueClosedTickets.column === 'createdDateTime'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'closedDateTime', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.closedTime')}
                                    {#if currentSort.overdueClosedTickets.column === 'closedDateTime'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'totalUsedTime', 'overdueClosedTickets')}>
                                    {t('dbChatPerformance.totalUsedTime')}
                                    {#if currentSort.overdueClosedTickets.column === 'totalUsedTime'}
                                        {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each overdueClosedTickets as item (item.id)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.ticketStatus}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getPriorityColor(item.priority)}">
                                            {item.priority}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getSentimentColor(item.sentiment)}">
                                            {item.sentiment}
                                        </span>
                                    </td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.closedDateTime)}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>
{/if}

<div class="flex flex-col gap-6">
    <div class="flex flex-col md:flex-row md:items-center justify-end gap-4 bg-white rounded-lg shadow-md p-4">
        <div class="flex flex-wrap items-center justify-start sm:justify-end gap-2 w-full md:w-auto">
            <label for="time-range-filter" class="text-gray-700 font-medium">{t('db.timeRange')}:</label>
            <select id="time-range-filter" bind:value={timeRange} class="border border-gray-300 rounded-md p-2 text-gray-700 w-full sm:w-auto">
                <option value="Last 7 Days">{t('db.last7Days')}</option>
                <option value="Last 30 Days">{t('db.last30Days')}</option>
                <option value="This Month">{t('db.thisMonth')}</option>
                <option value="Last Month">{t('db.lastMonth')}</option>
                <option value="Custom">{t('db.custom')}</option>
            </select>
            {#if timeRange === 'Custom'}
                <input type="date" bind:value={startDateCustom} class="border border-gray-300 rounded-md p-2 w-full sm:w-auto" />
                <input type="date" bind:value={endDateCustom} class="border border-gray-300 rounded-md p-2 w-full sm:w-auto" />
            {/if}
            <button
                on:click={clearFilters}
                class="bg-transparent hover:bg-gray-100 text-red-700 font-semibold py-2 px-4 border border-red-500 hover:border-transparent rounded-md text-sm mt-2 sm:mt-0 w-full sm:w-auto whitespace-nowrap flex-shrink-0" >
                {t('db.clearFilters')}
            </button>
            <!-- <button
                class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm mt-2 sm:mt-0 w-full sm:w-auto whitespace-nowrap flex-shrink-0" >
                {t('db.downloadCsvFullPage')}
            </button> -->
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <div class="lg:col-span-2 grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6">
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.totalIncomingMessages')}
                    value={chatVolume}
                    valueColor="text-black-600"
                    trendValue={chatVolumeTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.totalAgentTickets')}
                    value={allTickets}
                    valueColor="text-black-600"
                    trendValue={allTicketsTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.totalAgentClosedTickets')}
                    value={allClosedTickets}
                    valueColor="text-black-600"
                    trendValue={allClosedTicketsTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.agentTicketClosureRateVsIncoming')}
                    value={closedRate}
                    valueColor="text-black-600"
                    trendValue={closedRateTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.averageAgentResponseTimeSeconds')}
                    value={avgResponseTime}
                    valueColor="text-black-600"
                    trendValue={responseTimeTrend} trendUnit="%" isTrendPositiveIsGood={false}
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.agentResponseRateWithin6Seconds')}
                    value={responseRateWithin6s}
                    valueColor="text-black-600"
                    trendValue={responseRateWithin6sTrend} trendUnit="%"
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.averageAgentHandlingTimeMinutes')}
                    value={avgHandlingTime}
                    valueColor="text-black-600"
                    trendValue={handlingTimeRate} trendUnit="%" isTrendPositiveIsGood={false}
                />
            </div>
            <div class="bg-white rounded-lg shadow-md p-5">
                <ScoreCard
                    title={t('dbChatPerformance.agentHandlingRateWithin5Minutes')}
                    value={handlingRateWithin5mins}
                    valueColor="text-black-600"
                    trendValue={handlingRateWithin5minsTrend} trendUnit="%"
                />
            </div>
        </div>
        <div class="lg:col-span-1 bg-white p-5 rounded-lg shadow-md">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.agentTicketsByStatus')}</h2>
                <div class="flex gap-2">
                    <button
                        on:click={() => isTicketStatusChartExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.expand')}
                    </button>
                    <button
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-[28rem] sm:h-[28rem] lg:h-[28rem]">
                {#if isLoadingTicketStatusChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if ticketStatusChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if ticketStatusData.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={ticketStatusData}
                        chartType="horizontalBar"
                        labelKey="status"
                        valueKey="amount"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        <div class="bg-white p-6 rounded-lg shadow-md flex flex-col">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.agentClosedTicketsByCaseType')}</h2>
                <div class="flex gap-2">
                    <button
                        on:click={() => isCaseTypeChartExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.expand')}
                    </button>
                    <button
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-[26rem] sm:h-[26rem] lg:h-[26rem]">
                {#if isLoadingCaseTypeChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if caseTypeChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedTicketsByCaseType.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={closedTicketsByCaseType}
                        chartType="horizontalBar"
                        labelKey="caseType"
                        valueKey="count"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md flex flex-col">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.agentClosedTicketsBySubCaseType')}</h2>
                <div class="flex gap-2">
                    <button
                        on:click={() => isSubCaseTypeChartExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.expand')}
                    </button>
                    <button
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="w-full h-[26rem] sm:h-[26rem] lg:h-[26rem]">
                {#if isLoadingSubCaseTypeChart}
                    <div class="flex flex-col items-center justify-center h-full text-gray-600">
                        <svg class="animate-spin h-10 w-10 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-lg">{t('db.loadingChart')}</p>
                    </div>
                {:else if subCaseTypeChartError}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedTicketsBySubCaseType.length === 0}
                    <div class="text-gray-600 text-center text-lg h-full flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <BarChart
                        data={closedTicketsBySubCaseType}
                        chartType="horizontalBar"
                        labelKey="subCaseType"
                        valueKey="count"
                        label=""
                        barColor={COLORS.blue}
                        showValueLabels={true}
                    />
                {/if}
            </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-md overflow-y-auto">
            <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
                <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.agentClosedTicketsCaseAndSubCase')}</h2>
                <div class="flex gap-2">
                    <button
                        on:click={() => isCaseSubCaseTableExpanded = true}
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.expand')}
                    </button>
                    <button
                        class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                        {t('db.downloadExcel')}
                    </button>
                </div>
            </div>
            <div class="overflow-y-auto max-h-96">
                {#if isLoadingCaseSubCaseTable}
                    <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                        <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                    </div>
                {:else if caseSubCaseTableError}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else if closedCaseSubCaseTable.length === 0}
                    <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                        {t('db.noDataAvailable')}
                    </div>
                {:else}
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'caseType', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.caseType')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'caseType'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'subCaseType', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.subCaseType')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'subCaseType'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                                <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                    on:click={() => closedCaseSubCaseTable = sortTable(closedCaseSubCaseTable, 'count', 'closedCaseSubCaseTable')}>
                                    {t('dbChatPerformance.count')}
                                    {#if currentSort.closedCaseSubCaseTable.column === 'count'}
                                        {#if currentSort.closedCaseSubCaseTable.direction === 'asc'}▲{:else}▼{/if}
                                    {/if}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            {#each closedCaseSubCaseTable as item (item.caseType + item.subCaseType)}
                                <tr>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.caseType}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.subCaseType}</td>
                                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.count}</td>
                                </tr>
                            {/each}
                        </tbody>
                    </table>
                {/if}
            </div>
        </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md overflow-y-auto">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.unclosedTicketsOver1Day')}</h2>
            <div class="flex gap-2">
                <button
                    on:click={() => isOverdueUnclosedTicketsExpanded = true}
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                    {t('db.expand')}
                </button>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                    {t('db.downloadExcel')}
                </button>
            </div>
        </div>
        <div class="overflow-y-auto max-h-96">
            {#if isLoadingOverdueUnclosedTickets}
                <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                    <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                </div>
            {:else if overdueUnclosedTicketsError}
                <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                    {t('db.noDataAvailable')}
                </div>
            {:else if overdueUnclosedTickets.length === 0}
                <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                    {t('db.noDataAvailable')}
                </div>
            {:else}
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'ticketNo', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.ticketNo')}
                                {#if currentSort.overdueUnclosedTickets.column === 'ticketNo'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'ticketStatus', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.status')}
                                {#if currentSort.overdueUnclosedTickets.column === 'ticketStatus'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'customerName', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.customer')}
                                {#if currentSort.overdueUnclosedTickets.column === 'customerName'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'priority', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.priority')}
                                {#if currentSort.overdueUnclosedTickets.column === 'priority'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'sentiment', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.sentiment')}
                                {#if currentSort.overdueUnclosedTickets.column === 'sentiment'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'agentName', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.agent')}
                                {#if currentSort.overdueUnclosedTickets.column === 'agentName'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'createdDateTime', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.createdTime')}
                                {#if currentSort.overdueUnclosedTickets.column === 'createdDateTime'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'currentDateTime', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.currentTime')}
                                {#if currentSort.overdueUnclosedTickets.column === 'currentDateTime'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueUnclosedTickets = sortTable(overdueUnclosedTickets, 'totalUsedTime', 'overdueUnclosedTickets')}>
                                {t('dbChatPerformance.totalUsedTime')}
                                {#if currentSort.overdueUnclosedTickets.column === 'totalUsedTime'}
                                    {#if currentSort.overdueUnclosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each overdueUnclosedTickets as item (item.ticketNo)}
                            <tr>
                                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.ticketStatus}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getPriorityColor(item.priority)}">
                                        {item.priority}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getSentimentColor(item.sentiment)}">
                                        {item.sentiment}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.currentDateTime)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            {/if}
        </div>
    </div>

    <div class="bg-white p-6 rounded-lg shadow-md overflow-y-auto">
        <div class="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
            <h2 class="text-xl font-semibold text-gray-700">{t('dbChatPerformance.closedTicketsOver1Day')}</h2>
            <div class="flex gap-2">
                <button
                    on:click={() => isOverdueClosedTicketsExpanded = true}
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                    {t('db.expand')}
                </button>
                <button
                    class="bg-transparent hover:bg-gray-100 text-blue-700 font-semibold py-2 px-4 border border-blue-500 hover:border-transparent rounded-md text-sm whitespace-nowrap flex-shrink-0 w-full sm:w-auto" >
                    {t('db.downloadExcel')}
                </button>
            </div>
        </div>
        <div class="overflow-y-auto max-h-96">
            {#if isLoadingOverdueClosedTickets}
                <div class="flex flex-col items-center justify-center text-gray-600 h-48">
                    <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <p class="mt-4 text-sm">{t('db.loadingData')}</p>
                </div>
            {:else if overdueClosedTicketsError}
                <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                    {t('db.noDataAvailable')}
                </div>
            {:else if overdueClosedTickets.length === 0}
                <div class="text-gray-600 text-center text-lg h-48 flex items-center justify-center">
                    {t('db.noDataAvailable')}
                </div>
            {:else}
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50 sticky top-0 z-10">
                        <tr>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'ticketNo', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.ticketNo')}
                                {#if currentSort.overdueClosedTickets.column === 'ticketNo'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'ticketStatus', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.status')}
                                {#if currentSort.overdueClosedTickets.column === 'ticketStatus'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'customerName', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.customer')}
                                {#if currentSort.overdueClosedTickets.column === 'customerName'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'priority', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.priority')}
                                {#if currentSort.overdueClosedTickets.column === 'priority'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'sentiment', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.sentiment')}
                                {#if currentSort.overdueClosedTickets.column === 'sentiment'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'agentName', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.agent')}
                                {#if currentSort.overdueClosedTickets.column === 'agentName'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'createdDateTime', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.createdTime')}
                                {#if currentSort.overdueClosedTickets.column === 'createdDateTime'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'closedDateTime', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.closedTime')}
                                {#if currentSort.overdueClosedTickets.column === 'closedDateTime'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer"
                                on:click={() => overdueClosedTickets = sortTable(overdueClosedTickets, 'totalUsedTime', 'overdueClosedTickets')}>
                                {t('dbChatPerformance.totalUsedTime')}
                                {#if currentSort.overdueClosedTickets.column === 'totalUsedTime'}
                                    {#if currentSort.overdueClosedTickets.direction === 'asc'}▲{:else}▼{/if}
                                {/if}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {#each overdueClosedTickets as item (item.id)}
                            <tr>
                                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{item.ticketNo}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.ticketStatus}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.customerName}</td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getPriorityColor(item.priority)}">
                                        {item.priority}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {getSentimentColor(item.sentiment)}">
                                        {item.sentiment}
                                    </span>
                                </td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{item.agentName}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.createdDateTime)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatDateTimeForDisplay(item.closedDateTime)}</td>
                                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{formatOverdueTime(item.totalUsedTime)}</td>
                            </tr>
                        {/each}
                    </tbody>
                </table>
            {/if}
        </div>
    </div>
</div>